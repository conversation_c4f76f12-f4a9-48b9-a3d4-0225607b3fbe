'use client';

import { Suspense, useEffect, useMemo, useRef, useState } from 'react';
import { useAtom } from 'jotai';
import JobCard from '@/views/job/job-card';
import JobDetail from '@/views/job/job-detail';
import { useListJob, LIMIT, JobListResponse, Job } from '@/api-requests/job';
import Pagination from '@/views/pagination';
import cn from '@/utils/class-names';
import Loading from '../loading';
import {
  currentPageAtom,
  jobFilterParamsAtom,
  jobSearchParamsAtom,
} from '@/store/job-atom';
import { Loader } from 'rizzui';
import Image from 'next/image';
import { useProcessJobSimulation } from '@/api-requests/job/process-job-simulation';
import { useURLParamsManager } from '@/hooks/use-update-url-params';
import { useFavoriteJob } from '@/api-requests/job/favorite-job';
import { useUnfavoriteJob } from '@/api-requests/job/unfavorite-job';
import toast from 'react-hot-toast';

export function getJobTypeString(jobTypeFlags: Job): string {
  const types: string[] = [];

  if (jobTypeFlags.isFullTime) {
    types.push('Full Time');
  }

  if (jobTypeFlags.isHybrid) {
    types.push('Hybrid');
  }

  if (jobTypeFlags.isOnSite) {
    types.push('On-site');
  }

  if (jobTypeFlags.isRemote) {
    types.push('Remote');
  }

  if (jobTypeFlags.hasContract) {
    types.push('Contract');
  }

  if (jobTypeFlags.isPartTime) {
    types.push('Part Time');
  }

  if (types.length === 0) {
    return '';
  }

  // Nối các loại công việc bằng dấu phẩy
  return types.join(', ');
}

export const levelStyle: Record<string, string> = {
  Beginner: 'bg-[#CCFFE7] text-[#009A21] border border-[#009A21]',
  Intermediate: 'bg-[#FFE5CC] text-[#FF8C00] border border-[#FF8C00]',
  Advanced: 'bg-[#E5CCFF] text-[#800080] border border-[#800080]',
};

export const simulationLevel: Record<number, string> = {
  1: 'Beginner',
  2: 'Intermediate',
  3: 'Advanced',
};

function JobListContent() {
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  console.log('🚀 ~ JobListContent ~ selectedJob:', selectedJob);
  const [jobInfo, setJobInfo] = useState<JobListResponse>();

  const [page, setPage] = useAtom(currentPageAtom);
  const [searchParams] = useAtom(jobSearchParamsAtom);
  const [filterParams] = useAtom(jobFilterParamsAtom);

  const urlManager = useURLParamsManager();
  const jobIdUrl = urlManager.get('id') as string;

  const isRemovingId = useRef(false);

  const queryParams = useMemo(() => {
    return {
      limit: LIMIT,
      page,
      title: searchParams.title || undefined,
      location: searchParams.location || undefined,
      categories: filterParams.categories || undefined,
      jobType:
        filterParams.jobType.length > 0
          ? filterParams.jobType.join(',')
          : undefined,
      salary:
        filterParams.salary.length > 0
          ? filterParams.salary.join(',')
          : undefined,
      level:
        filterParams.difficulty.length > 0
          ? filterParams.difficulty.join(',')
          : undefined,
      id: jobIdUrl || undefined,
    };
  }, [
    page,
    searchParams.title,
    searchParams.location,
    filterParams.categories,
    filterParams.jobType,
    filterParams.salary,
    filterParams.difficulty,
    jobIdUrl,
  ]);

  const { data, isLoading, refetch } = useListJob(queryParams);

  const { mutateAsync, isPending } = useProcessJobSimulation();

  const { mutateAsync: mutateAsyncFavorite, isPending: isPendingFavorite } =
    useFavoriteJob();
  const { mutateAsync: mutateAsyncUnfavorite, isPending: isPendingUnfavorite } =
    useUnfavoriteJob();

  useEffect(() => {
    if (isLoading) return;
    setJobInfo(data);

    if (jobIdUrl && data && !isRemovingId.current) {
      const job = data?.data?.find((job) => job.jobId === jobIdUrl);
      setSelectedJob(job || data?.data?.[0] || null);

      isRemovingId.current = true;
      urlManager.remove('id');
      setTimeout(() => {
        isRemovingId.current = false;
      }, 100);
    }
  }, [jobIdUrl, isLoading, data, urlManager]);

  useEffect(() => {
    if (isLoading || isRemovingId.current) return;

    setJobInfo(data);
    const job = data?.data?.find(
      (job) => job.jobId === (selectedJob?.jobId as string)
    );
    setSelectedJob(job || data?.data?.[0] || null);
  }, [data, isLoading, selectedJob]);

  const jobs = jobInfo?.data || [];
  const meta = jobInfo?.meta || { total: 0 };

  const isSearching = Boolean(
    searchParams.title ||
      searchParams.location ||
      filterParams.categories ||
      filterParams.jobType.length > 0 ||
      filterParams.salary.length > 0
  );

  useEffect(() => {
    if (selectedJob && !selectedJob.simulation?.id) {
      mutateAsync({
        jobId: selectedJob.jobId,
      })
        .then((data) => {
          if (data.simulation?.id) {
            refetch();
          }
        })
        .catch((error) =>
          console.error('Failed to update job simulation:', error)
        );
    }
  }, [selectedJob, mutateAsync, refetch]);

  const handleFavorite = async (job: Job) => {
    let resp = null;
    console.log('🚀 ~ handleFavorite ~ job?.interaction:', job?.interaction);
    if (job?.interaction?.value) {
      resp = await mutateAsyncUnfavorite(job.jobId);
    } else {
      resp = await mutateAsyncFavorite(job.jobId);
    }

    if (resp) {
      toast.success(
        job?.interaction?.value
          ? 'Removed from favorites'
          : 'Added to favorites'
      );
      refetch();
    } else {
      toast.error('Something went wrong. Please try again.');
    }
  };

  return (
    <div className="relative">
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
        {isLoading && jobs.length === 0 ? (
          <Loading />
        ) : jobs.length > 0 ? (
          <>
            {isLoading && (
              <div className="absolute left-[50%] top-[300px] z-20 -translate-x-[50%]">
                <Loader size="xl" />
              </div>
            )}

            {/* Job List */}
            <div
              className={cn(
                'flex flex-col gap-5',
                selectedJob
                  ? 'scrollbar-hide lg:sticky lg:top-[140px] lg:col-span-1 lg:max-h-[calc(100vh-140px)] lg:self-start lg:overflow-auto'
                  : 'lg:col-span-3'
              )}
              style={{
                msOverflowStyle: 'none',
                scrollbarWidth: 'none',
              }}
            >
              <div
                className={cn(
                  'flex justify-between gap-4 text-sm',
                  isLoading && 'blur-[3px]'
                )}
              >
                <b>Search Results</b>
                <span>
                  <b>{meta?.total}</b> jobs found
                </span>
              </div>

              <div
                className={cn(
                  selectedJob
                    ? 'grid grid-cols-1 gap-4'
                    : 'grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3',
                  isLoading && 'blur-[3px]'
                )}
              >
                {jobs.map((job, index) => (
                  <JobCard
                    key={index}
                    job={job}
                    setSelectedJob={setSelectedJob}
                    selectedJob={selectedJob}
                    onFavorite={handleFavorite}
                    isLoading={
                      isLoading || isPendingFavorite || isPendingUnfavorite
                    }
                  />
                ))}
              </div>

              <Pagination
                total={meta?.total}
                current={page}
                pageSize={LIMIT}
                defaultCurrent={1}
                showLessItems={true}
                onChange={(page: number) => {
                  setPage(page);
                  urlManager.update({ page: page.toString() });
                }}
                prevIconClassName="py-0 text-gray-500 !leading-[26px]"
                nextIconClassName="py-0 text-gray-500 !leading-[26px]"
                disabled={isLoading}
                variant="solid"
                className="justify-center [&>.rc-pagination-item-active>a]:!text-white [&>.rc-pagination-item-active]:!border-primary [&>.rc-pagination-item-active]:!bg-primary [&>.rc-pagination-item-active]:!text-white [&>.rc-pagination-item:not(.rc-pagination-item-active)>a]:text-black [&>.rc-pagination-item:not(.rc-pagination-item-active)]:bg-transparent [&>.rc-pagination-item]:rounded-lg"
              />
            </div>
          </>
        ) : (
          <div className="col-span-3">
            {isSearching ? (
              <div
                // className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3"
                className="flex justify-center"
              >
                <div className="flex flex-col items-center justify-center rounded-xl bg-[#F9F9F9] p-4 px-24 py-14">
                  <Image
                    src="/job/search-empty.png"
                    alt="search-empty"
                    width={92}
                    height={92}
                    loader={({ src }) => src}
                  />
                  <div className="text-sm opacity-50">
                    Can&apos;t find a suitable job
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex h-[234px] items-center justify-center">
                <Image
                  src="/job/empty-job.png"
                  alt="no-job"
                  width={120}
                  height={0}
                  className="h-auto w-[120px]"
                  loader={({ src }) => src}
                />
              </div>
            )}
          </div>
        )}

        {/* Job Detail */}
        {selectedJob && (
          <div className="mt-10 lg:col-span-2">
            <div
              className="scrollbar-hide lg:sticky lg:top-[140px] lg:max-h-[calc(100vh-140px)] lg:overflow-auto"
              style={{
                msOverflowStyle: 'none',
                scrollbarWidth: 'none',
              }}
            >
              <JobDetail
                job={selectedJob}
                isLoading={isLoading || isPendingFavorite || isPendingUnfavorite}
                creatingSimulation={isPending || isLoading}
                onFavorite={handleFavorite}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default function JobList() {
  return (
    <Suspense fallback={<></>}>
      <JobListContent />
    </Suspense>
  );
}
