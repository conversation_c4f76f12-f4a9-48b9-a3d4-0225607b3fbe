import JobBanner from '@/views/banner/job-banner';
import JobFooter from '@/views/banner/job-banner/footer';
import HowItWorks from '@/views/banner/job-banner/how-it-work';
import JobSuggestion from '@/views/banner/job-banner/job-suggestion';
import RecentWins from '@/views/banner/job-banner/recent-win';
import JobFeatureCard from '@/views/job-feature-card';
import SmarterWay from '@/views/smarter-way';

export default function JobFeatureCards() {
  return (
    <section>
      <JobBanner />

      {/* <div className="bg-gray-50">
        <JobSuggestion />
      </div> */}

      {/* <div
        className={
          'relative z-10 mx-auto flex w-full max-w-[1440px] flex-col gap-14 px-4 py-14 xl:px-0 xl:py-20'
        }
      >
        <JobFeatureCard />
      </div> */}

      <div className="mx-auto w-full max-w-[1440px] px-4 py-8 xl:px-0 xl:py-12">
        <HowItWorks />
      </div>

      <div className="bg-[#F8F8F8]">
        <RecentWins />

        <SmarterWay />
      </div>

      <JobFooter />

      {/* <div
        className="absolute bottom-0 left-0 z-0 h-[220px] w-full bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: 'url("/job/job-footer-bg.png")' }}
      ></div> */}
    </section>
  );
}
