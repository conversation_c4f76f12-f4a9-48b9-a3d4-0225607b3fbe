'use client';

import {
  <PERSON>,
  Checkbox,
  Badge,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Loader,
  Avatar,
  Dropdown,
} from 'riz<PERSON><PERSON>';
import MessageIcon from '@/views/icons/message';
import EditSquareIcon from '@/views/icons/edit-square';
import Image from 'next/image';
import Pagination from '../pagination';
import { useState } from 'react';
import { JobCandidate } from '@/api-requests/job-candidate/types';
import { ApiListResponse, LIMIT } from '@/api-requests/types';
import { MoreHorizontalIcon } from 'lucide-react';
import DownloadCvIcon from '../icons/download-cv';
import HeartOutlineIcon from '../icons/heart-outline';
import { ShortlistCandidate } from '@/api-requests/shortlist-candidate';

const badgeClasses: Record<string, string> = {
  active: 'bg-[#FFE2C0] text-[#CD6B01]',
  completed: 'bg-[#FFD8D8] text-[#B90707]',
};

interface IProps {
  jobCandidateData: ApiListResponse<ShortlistCandidate>;
  isLoading: boolean;
  page: number;
  setPage: (page: number) => void;
  onClickAction: (action: string, candidate: ShortlistCandidate) => void;
  onCheckboxChange: (
    checked: boolean,
    candidate: ShortlistCandidate | string
  ) => void;
  selectedCandidates: ShortlistCandidate[];
}

export default function CandidateTable({
  jobCandidateData,
  isLoading,
  page,
  setPage,
  onClickAction,
  onCheckboxChange,
  selectedCandidates,
}: IProps) {
  const actions = [
    {
      icon: <MessageIcon className="h-5 w-5" />,
      label: 'Message',
    },
    {
      icon: <DownloadCvIcon className="h-5 w-5" />,
      label: 'Download CV',
    },
    {
      icon: <HeartOutlineIcon className="h-5 w-5" />,
      label: 'Add to Shortlist',
    },
    {
      icon: <EditSquareIcon className="h-5 w-5" />,
      label: 'View Detail',
    },
  ];

  return (
    <div>
      {/* <div className="mb-3 flex items-center gap-4">
        <div className="text-sm">3 selected</div>
        <button className="rounded-md border border-primary px-3 py-1 text-sm text-black hover:bg-primary hover:text-white">
          Send message
        </button>
        <button className="rounded-md border border-primary px-3 py-1 text-sm text-black hover:bg-primary hover:text-white">
          Remove
        </button>
      </div> */}
      <div className="rounded-xl bg-white pb-5 shadow-lg">
        <div className="overflow-x-auto">
          <Table>
            <Table.Header className="rounded-t-xl !bg-[#FAFAFA]">
              <Table.Row>
                <Table.Head className="w-4">
                  <Checkbox
                    variant="flat"
                    size="sm"
                    checked={
                      selectedCandidates.length ===
                        jobCandidateData?.data?.length &&
                      jobCandidateData?.data?.length > 0
                    }
                    indeterminate={
                      selectedCandidates.length > 0 &&
                      selectedCandidates.length <
                        (jobCandidateData?.data?.length || 0)
                    }
                    onChange={(e) => onCheckboxChange(e.target.checked, 'all')}
                  />
                </Table.Head>
                <Table.Head className="w-[20%]">Candidate name</Table.Head>
                <Table.Head className="w-[100px]">Matches</Table.Head>
                <Table.Head className="w-[20%]">Simulation</Table.Head>
                <Table.Head className="w-full">AI Review</Table.Head>
                <Table.Head className="!text-right">Actions</Table.Head>
              </Table.Row>
            </Table.Header>

            <Table.Body>
              {jobCandidateData?.data?.map((candidate, index) => (
                <Table.Row key={index}>
                  <Table.Cell>
                    <Checkbox
                      variant="flat"
                      size="sm"
                      checked={selectedCandidates.some(
                        (c) => c._id === candidate._id
                      )}
                      onChange={(e) =>
                        onCheckboxChange(e.target.checked, candidate)
                      }
                    />
                  </Table.Cell>

                  <Table.Cell>
                    <div
                      className="flex cursor-pointer items-center gap-3"
                      onClick={() => {
                        onClickAction('View Detail', candidate);
                      }}
                    >
                      <Avatar
                        src={
                          candidate.user?.avatar || '/avatar/user-default.png'
                        }
                        name={
                          candidate.user?.firstName +
                          ' ' +
                          candidate.user?.lastName
                        }
                        customSize={40}
                        className='!bg-transparent'
                      />
                      <div className="text-left">
                        <div className="font-medium text-gray-900">
                          {candidate.user?.firstName +
                            ' ' +
                            candidate.user?.lastName}
                        </div>
                        {/* <div className="text-xs text-gray-500">
                        {candidate.country}
                      </div> */}
                      </div>
                    </div>
                  </Table.Cell>

                  <Table.Cell>
                    <span className="font-semibold text-gray-800">
                      {candidate.matchPercentage || 0}%
                    </span>
                  </Table.Cell>

                  <Table.Cell>
                    <div>{candidate.simulation?.name}</div>
                    <Badge
                      variant="flat"
                      size="sm"
                      className={badgeClasses[candidate.status]}
                    >
                      {candidate.status}
                    </Badge>
                  </Table.Cell>

                  <Table.Cell>
                    <div>{candidate.aiEvaluation?.summary || '-'}</div>
                    {/* <ul className="list-inside list-disc space-y-1 text-sm text-gray-700">
                    {candidate.review.map((line, i) => (
                      <li key={i}>{line}</li>
                    ))}
                  </ul> */}
                  </Table.Cell>

                  <Table.Cell className="text-right">
                    <div className="flex justify-end gap-3">
                      <Dropdown placement="bottom-end">
                        <Dropdown.Trigger>
                          <MoreHorizontalIcon />
                        </Dropdown.Trigger>
                        <Dropdown.Menu className="w-fit divide-y">
                          {actions.map((action, idx) => (
                            <Dropdown.Item
                              key={idx}
                              className="hover:bg-primary hover:text-white"
                              onClick={() =>
                                onClickAction(action.label, candidate)
                              }
                            >
                              <div className="flex items-center">
                                {action.icon}
                                <span className="ml-2">{action.label}</span>
                              </div>
                            </Dropdown.Item>
                          ))}
                        </Dropdown.Menu>
                      </Dropdown>
                    </div>
                  </Table.Cell>
                </Table.Row>
              ))}

              {isLoading ? (
                <Table.Row>
                  <Table.Cell colSpan={7} className="h-40 text-center">
                    <div className="flex min-h-40 items-center justify-center">
                      <Loader className="h-8 w-8" />
                    </div>
                  </Table.Cell>
                </Table.Row>
              ) : (
                jobCandidateData?.data?.length === 0 && (
                  <Table.Row>
                    <Table.Cell colSpan={7} className="text-center">
                      <div className="text-gray-500">No candidates found</div>
                    </Table.Cell>
                  </Table.Row>
                )
              )}
            </Table.Body>
          </Table>
        </div>

        <hr className="border-t border-gray-100 pt-4" />

        <Pagination
          total={jobCandidateData?.meta.total || 0}
          current={page}
          pageSize={LIMIT}
          defaultCurrent={1}
          showLessItems={true}
          onChange={(page: number) => setPage(page)}
          prevIconClassName="py-0 text-gray-500 !leading-[26px]"
          nextIconClassName="py-0 text-gray-500 !leading-[26px]"
          className="justify-center [&>.rc-pagination-item-active>a]:!text-white [&>.rc-pagination-item-active]:!border-primary [&>.rc-pagination-item-active]:!bg-primary [&>.rc-pagination-item-active]:!text-white [&>.rc-pagination-item:not(.rc-pagination-item-active)>a]:text-black [&>.rc-pagination-item:not(.rc-pagination-item-active)]:bg-transparent [&>.rc-pagination-item]:rounded-lg"
          variant="solid"
        />
      </div>
    </div>
  );
}
