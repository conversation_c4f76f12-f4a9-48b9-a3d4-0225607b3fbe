'use client';

import { But<PERSON> } from 'rizzui';
import Stepper from './stepper';
import StepOne from './step-one';
import { useAtom } from 'jotai';
import { jobCreationStepAtom } from '@/store/job-creation-atom';
import { useCallback } from 'react';
import ArrowLeftIcon from '../icons/arrow-left';
import ArrowRightIcon from '../icons/arrow-right';
import StepTwo from './step-two';
import StepThree from './step-three';
import { FormProvider, useForm } from 'react-hook-form';
import { useCreateJob } from '@/api-requests/job/create-job';
import toast from 'react-hot-toast';
import { Job, Salary } from '@/api-requests/job';
import { API_DOMAINS } from '@/config/endpoint';
import { parseApplyMode } from './helper';
import { useUpdateJob } from '@/api-requests/job/update-job';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

export type FormValues = {
  title: string;
  categories?: string[];
  levels?: string[];
  applyMode?: string[];
  // address?: string;
  // city?: string;
  // region?: string;
  // country: {
  //   label: string;
  //   value: string;
  // } | null;
  employment?: {
    isFullTime?: boolean;
    isPartTime?: boolean;
    hasContract?: boolean;
  };
  workplace?: {
    isOnSite?: boolean;
    isRemote?: boolean;
    isHybrid?: boolean;
  };
  salary: {
    min?: number;
    max?: number;
    currency?: string;
    period?: string;
  };
  experience: {
    min?: number;
    max?: number;
  };
  description?: string;
  skills: string[];
  expiresAt?: Date | null;
  applicationLimit?: number | null;
};

interface IProps {
  job?: Job | null;
  type?: 'create' | 'update';
}

export default function JobCreation({ job, type }: IProps) {
  const router = useRouter();

  const { data, mutateAsync, isPending } = useCreateJob();
  const { mutateAsync: updateMutateAsync, isPending: isUpdating } =
    useUpdateJob(job?.jobId || '');
  const [step, setStep] = useAtom(jobCreationStepAtom);

  const methods = useForm<FormValues>({
    mode: 'onChange',
    defaultValues: {
      title: job?.title || '',
      categories: job?.categories || [],
      levels: job?.levels || [],
      // address: '',
      // city: '',
      // region: '',
      // country: null,
      employment: {
        isFullTime: job?.isFullTime || false,
        isPartTime: job?.isPartTime || false,
        hasContract: job?.hasContract || false,
      },
      workplace: {
        isOnSite: job?.isOnSite || false,
        isRemote: job?.isRemote || false,
        isHybrid: job?.isHybrid || false,
      },
      salary: {
        min: (job?.salary as Salary)?.min || 1000,
        max: (job?.salary as Salary)?.max || 100000,
        currency: (job?.salary as Salary)?.currency || 'USD',
        period: (job?.salary as Salary)?.period || 'month',
      },
      // experience: {
      //   min: 0,
      //   max: 0,
      // },
      description: job?.description || '',
      skills: job?.skills || [],
      expiresAt: job?.expiresAt || null,
      applyMode: parseApplyMode(job?.applyMode as string) || [
        'simulation',
        'cv',
      ],
      applicationLimit: job?.applicationLimit || null,
    },
  });

  const renderStepContent = useCallback(() => {
    switch (step) {
      case 1:
        return <StepOne />;
      case 2:
        return <StepTwo />;
      case 3:
        return <StepThree />;
      default:
        return '';
    }
  }, [step]);

  const onSubmitDraft = async (payload: FormValues) => {
    const jobData = {
      ...payload.employment,
      ...payload.workplace,
      salary: payload.salary,
      title: payload.title,
      categories: payload.categories,
      levels: payload.levels,
      description: payload.description,
      skills: payload.skills,
      jobUrl: API_DOMAINS.BASE_URL + '/find-jobs',
      expiresAt: payload.expiresAt,
      isPublished: false,
      applyMode: payload.applyMode,
      applicationLimit: payload.applicationLimit,
    } as Job;

    let resp = null;
    if (type === 'update') {
      resp = await updateMutateAsync(jobData);
    } else {
      resp = await mutateAsync(jobData);
    }

    if (resp) {
      toast.success(
        `Job ${type === 'update' ? 'updated' : 'created'} successfully`
      );
      setStep(1);
      methods.reset();
    } else {
      toast.error(`Failed to ${type === 'update' ? 'update' : 'create'} job`);
    }
  };

  const onSubmit = async (payload: FormValues) => {
    const jobData = {
      ...payload.employment,
      ...payload.workplace,
      salary: payload.salary,
      title: payload.title,
      categories: payload.categories,
      levels: payload.levels,
      description: payload.description,
      skills: payload.skills,
      jobUrl: API_DOMAINS.BASE_URL + '/find-jobs',
      expiresAt: payload.expiresAt,
      isPublished: true,
      applyMode: payload.applyMode,
      applicationLimit: payload.applicationLimit,
    } as Job;

    let resp = null;
    if (type === 'update') {
      resp = await updateMutateAsync(jobData);
    } else {
      resp = await mutateAsync(jobData);
    }
    if (resp && resp.isSuccess !== false) {
      toast.success(
        `Job ${type === 'update' ? 'updated' : 'created'} successfully`
      );
      setStep(1);
      methods.reset();
    } else {
      toast.error(
        resp?.message ||
          `Failed to ${type === 'update' ? 'update' : 'create'} job`
      );
    }
  };

  const onNext = () => {
    setStep((prev) => prev + 1);
  };

  const hanleBack = () => {
    setStep((prev) => prev - 1);
  };

  return (
    <div>
      <div className="flex items-center justify-between">
        <div>{type === 'update' ? 'Update Job' : 'Create a New Job'} </div>
        <Link href="/org/admin/jobs">
          <Button
            variant="outline"
            className="border border-[#00B074] hover:bg-primary hover:text-white"
            size="sm"
          >
            Cancel
          </Button>
        </Link>
      </div>

      <hr className="my-4" />

      <Stepper />

      <div className="mx-auto max-w-[792px] space-y-6 py-10">
        <FormProvider {...methods}>{renderStepContent()}</FormProvider>

        <hr className="text-[#222222]" />

        <div className="flex flex-wrap items-center justify-between gap-4">
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={hanleBack}
            disabled={step === 1}
          >
            <ArrowLeftIcon className="h-5 w-5" />
            Back
          </Button>

          <Button
            className="text-[#161616] underline underline-offset-4 hover:opacity-80"
            variant="flat"
            disabled={
              !methods.formState.isValid ||
              isPending ||
              methods.formState.isSubmitting
            }
          >
            Save as Draft
          </Button>

          {step === 3 ? (
            <div className="flex items-center gap-4">
              <Button
                className="flex items-center gap-2 bg-primary text-white"
                onClick={methods.handleSubmit(onSubmitDraft)}
                disabled={
                  !methods.formState.isValid ||
                  isPending ||
                  isUpdating ||
                  methods.formState.isSubmitting
                }
                isLoading={
                  isPending || isUpdating || methods.formState.isSubmitting
                }
              >
                Create
                <ArrowRightIcon className="h-5 w-5 text-white" />
              </Button>
              <Button
                className="flex items-center gap-2 bg-primary text-white"
                onClick={methods.handleSubmit(onSubmit)}
                disabled={
                  !methods.formState.isValid ||
                  isPending ||
                  isUpdating ||
                  methods.formState.isSubmitting
                }
                isLoading={
                  isPending || isUpdating || methods.formState.isSubmitting
                }
              >
                Create and Publish
                <ArrowRightIcon className="h-5 w-5 text-white" />
              </Button>
            </div>
          ) : (
            <Button
              className="flex items-center gap-2 bg-primary text-white"
              onClick={methods.handleSubmit(onNext)}
            >
              Next
              <ArrowRightIcon className="h-5 w-5 text-white" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
