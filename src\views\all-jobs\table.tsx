'use client';

import { <PERSON>, Badge, Tooltip, Button, Avatar, Loader } from 'riz<PERSON><PERSON>';
import EditSquareIcon from '@/views/icons/edit-square';
import Pagination from '@/views/pagination';
import cn from '@/utils/class-names';
import EditIcon from '@/views/icons/edit';
import PlusSquareIcon from '@/views/icons/plus-square';
import DeleteIcon from '@/views/icons/delete';
import { useRouter } from 'next/navigation';
import { Job } from '@/api-requests/job';
import { ApiListResponse, LIMIT } from '@/api-requests/types';
import DeleteJobModal from './delete-job-modal';
import { useState } from 'react';

const badgeClasses: Record<string, string> = {
  false: 'bg-[#E8E8E8] text-[#161616]',
  true: 'bg-[#98FFDC] text-[#008457]',
};

interface IProps {
  jobData: ApiListResponse<Job>;
  isLoading: boolean;
  page: number;
  setPage: (page: number) => void;
  refetch: () => void;
}

export default function AllJobsTable({
  jobData,
  isLoading,
  page,
  setPage,
  refetch,
}: IProps) {
  const router = useRouter();

  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);

  const handleView = (id: string) => {
    router.push(`/org/admin/jobs/${id}`);
  };

  const handleEdit = (id: string) => {
    router.push(`/org/admin/jobs/edit/${id}`);
  };

  return (
    <>
      <div className="rounded-xl bg-white pb-5 shadow-lg">
        <Table variant="modern">
          <Table.Header className="rounded-t-xl !bg-[#FAFAFA]">
            <Table.Row className="rounded-t-xl">
              {/* <Table.Head className="w-16"></Table.Head> */}
              <Table.Head>Job title</Table.Head>
              <Table.Head>Candidates</Table.Head>
              <Table.Head>Avg Match</Table.Head>
              <Table.Head>Simulation</Table.Head>
              <Table.Head>Status</Table.Head>
              <Table.Head className="!text-right">Actions</Table.Head>
            </Table.Row>
          </Table.Header>

          <Table.Body>
            {jobData?.data?.map((job, index) => (
              <Table.Row key={index}>
                {/* <Table.Cell>
                  <Avatar
                    src={job.companyLogoUrl}
                    name={job.title}
                    customSize={40}
                  />
                </Table.Cell> */}

                <Table.Cell
                  className="cursor-pointer text-sm"
                  onClick={() => handleView(job.jobId)}
                >
                  <span className="text-sm">{job.title}</span>
                </Table.Cell>

                <Table.Cell>
                  <span className="text-sm font-bold">{job.applicants}</span>
                </Table.Cell>

                <Table.Cell>
                  <span className="text-sm font-bold">-</span>
                </Table.Cell>

                <Table.Cell>
                  <span className="text-sm">
                    {job.simulation ? 'Yes' : 'No'}
                  </span>
                </Table.Cell>

                <Table.Cell>
                  <Badge
                    variant="flat"
                    size="sm"
                    className={cn(
                      badgeClasses[String(job.isPublished)],
                      'text-[12px] font-medium'
                    )}
                  >
                    {job.isPublished ? 'Published' : 'Unpublished'}
                  </Badge>
                </Table.Cell>

                <Table.Cell className="text-right">
                  <div className="flex justify-end gap-3">
                    <Tooltip content="Edit" color="invert">
                      <Button
                        variant="text"
                        size="sm"
                        className="bg-[#F9F9F9] text-gray-500 hover:text-black"
                        onClick={() => handleEdit(job.jobId)}
                      >
                        <EditIcon className="h-4 w-4" />
                      </Button>
                    </Tooltip>
                    {/* <Tooltip color="invert" content="Add">
                      <Button
                        variant="text"
                        size="sm"
                        className="bg-[#F9F9F9] text-gray-500 hover:text-black"
                        // onClick={() => handleView(job._id.toString())}
                      >
                        <PlusSquareIcon className="h-4 w-4" />
                      </Button>
                    </Tooltip> */}
                    {/* <Tooltip color="invert" content="View">
                      <Button
                        variant="text"
                        size="sm"
                        className="bg-[#F9F9F9] text-gray-500 hover:text-black"
                        onClick={() => handleView(job.jobId)}
                      >
                        <EditSquareIcon className="h-4 w-4" />
                      </Button>
                    </Tooltip> */}
                    <Tooltip color="invert" content="Remove">
                      <Button
                        variant="text"
                        size="sm"
                        className="bg-[#F9F9F9] text-gray-500 hover:text-black"
                        onClick={() => {
                          // TODO: What if the job has applications (?)
                          setSelectedJob(job);
                          setOpenDeleteModal(true);
                        }}
                      >
                        <DeleteIcon className="h-4 w-4" />
                      </Button>
                    </Tooltip>
                  </div>
                </Table.Cell>
              </Table.Row>
            ))}

            {isLoading ? (
              <Table.Row>
                <Table.Cell colSpan={7} className="h-40 text-center">
                  <div className="flex min-h-40 items-center justify-center">
                    <Loader className="h-8 w-8" />
                  </div>
                </Table.Cell>
              </Table.Row>
            ) : (
              jobData?.data?.length === 0 && (
                <Table.Row>
                  <Table.Cell colSpan={7} className="text-center">
                    <div className="text-gray-500">No jobs found</div>
                  </Table.Cell>
                </Table.Row>
              )
            )}
          </Table.Body>
        </Table>

        <hr className="border-t border-gray-100 pt-4" />

        <Pagination
          total={jobData?.meta.total || 0}
          current={page}
          pageSize={LIMIT}
          defaultCurrent={1}
          showLessItems={true}
          onChange={(page: number) => setPage(page)}
          prevIconClassName="py-0 text-gray-500 !leading-[26px]"
          nextIconClassName="py-0 text-gray-500 !leading-[26px]"
          className="justify-center [&>.rc-pagination-item-active>a]:!text-white [&>.rc-pagination-item-active]:!border-primary [&>.rc-pagination-item-active]:!bg-primary [&>.rc-pagination-item-active]:!text-white [&>.rc-pagination-item:not(.rc-pagination-item-active)>a]:text-black [&>.rc-pagination-item:not(.rc-pagination-item-active)]:bg-transparent [&>.rc-pagination-item]:rounded-lg"
          variant="solid"
        />
      </div>

      {openDeleteModal && (
        <DeleteJobModal
          open={openDeleteModal}
          onClose={() => {
            setSelectedJob(null);
            setOpenDeleteModal(false);
            refetch();
          }}
          job={selectedJob as Job}
        />
      )}
    </>
  );
}
