'use client';

import { useGetCandidatesByOrg } from '@/api-requests/job-candidate/get-candidate-by-email';
import { debounce } from 'lodash';
import { orgAtom } from '@/store/organization-atom';
import { Button } from 'rizzui';
import ShortlistModal from '../shortlist/shortlist-modal';
import { Shortlist, useGetShorlistByOrg } from '@/api-requests/shortlist';
import {
  ShortlistCandidate,
  useCreateShortlistCandidate,
  useDeleteShortListCandidate,
} from '@/api-requests/shortlist-candidate';
import { useCreateShortlist } from '@/api-requests/shortlist/create';
import { ApiListResponse, LIMIT, SelectOption } from '@/api-requests/types';
import { userAtom } from '@/store/user-atom';
import { useAtom } from 'jotai';
import { useRouter, useSearchParams } from 'next/navigation';
import { useBulkCreateShortlistCandidate } from '@/api-requests/shortlist-candidate/bulk-create';
import { useBulkDeleteShortlistCandidate } from '@/api-requests/shortlist-candidate/bulk-delete';
import { useEffect, useRef, useState } from 'react';
import toast from 'react-hot-toast';
import CreateShortlistModal from '../shortlist/create-shortlist-modal';
import CandidateFilter from './candidate-filter';
import CandidateTable from './candidate-table';

interface IProps {
  setView: (view: 'shortlist' | 'candidate') => void;
}

export default function CandidateManagement({ setView }: IProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [user] = useAtom(userAtom);
  const [org] = useAtom(orgAtom);
  const [page, setPage] = useState(1);
  const [status, setStatus] = useState<SelectOption | null>(null);
  const [search, setSearch] = useState<string>('');
  const [candidateSeleted, setCandidateSeleted] =
    useState<ShortlistCandidate | null>(null);
  const [openShortlist, setOpenShortlist] = useState(false);
  const [openCreateShortlist, setOpenCreateShortlist] = useState(false);
  const [selectedCandidates, setSelectedCandidates] = useState<
    ShortlistCandidate[]
  >([]);

  const searchRef = useRef<HTMLInputElement>(null!);

  const { data, isLoading } = useGetCandidatesByOrg(org?._id as string, {
    page,
    limit: LIMIT,
    name: search,
    status: status?.value as 'active' | 'completed',
  });

  const { data: shortlists, refetch } = useGetShorlistByOrg(org?._id as string);
  const { mutateAsync, isPending: isDeleting } = useDeleteShortListCandidate();
  const { mutateAsync: bulkDeleteMutateAsync, isPending: isBulkDeleting } =
    useBulkDeleteShortlistCandidate();
  const { mutateAsync: createMutateAsync, isPending: isCreating } =
    useCreateShortlistCandidate();
  const { mutateAsync: bulkCreateMutateAsync, isPending: isBulkCreating } =
    useBulkCreateShortlistCandidate();
  const {
    mutateAsync: createShortlistMutateAsync,
    isPending: creatingShortlist,
  } = useCreateShortlist();

  const handleChangeStatus = (option: SelectOption | null) => {
    setStatus(option);
    setPage(1);
  };

  const handleSearch = debounce((value: string) => {
    setSearch(value);
    setPage(1);
  }, 500);

  const handleClickAction = (action: string, candidate: ShortlistCandidate) => {
    setCandidateSeleted(candidate);

    switch (action) {
      case 'Message':
        break;
      case 'Download CV':
        break;
      case 'Add to Shortlist':
        setSelectedCandidates([]);
        setOpenShortlist(true);
        break;
      case 'View Detail':
        router.push(`/org/admin/candidates/${candidate._id}`);
        break;
      default:
        break;
    }
  };

  const handleCreateShortlistCandidate = async (
    checked: boolean,
    list: Shortlist,
    candidate: ShortlistCandidate
  ) => {
    if (checked) {
      const resp = await createMutateAsync({
        candidateId: candidate?._id as string,
        shortlistId: list._id,
        orgId: org?._id as string,
      });

      if (resp) {
        toast.success('Candidate added to shortlist');
        refetch();
        // setOpenShortlist(false);
      } else {
        toast.error('Failed to add candidate to shortlist');
      }
      return;
    } else {
      const resp = await mutateAsync({
        candidateId: candidate?._id as string,
        shortlistId: list._id,
        orgId: org?._id as string,
      });

      if (resp) {
        toast.success('Candidate removed from shortlist');
        refetch();
        // setOpenShortlist(false);
      } else {
        toast.error('Failed to remove candidate from shortlist');
      }
      return;
    }
  };

  const handleCreateShortlistCandidates = async (
    checked: boolean,
    list: Shortlist,
    candidates: ShortlistCandidate[]
  ) => {
    if (checked) {
      const resp = await bulkCreateMutateAsync({
        candidateIds: candidates.map((c) => c._id),
        shortlistId: list._id,
        orgId: org?._id as string,
      });

      if (resp) {
        toast.success('Candidates added to shortlist');
        refetch();
        setSelectedCandidates([]);
        setOpenShortlist(false);
      } else {
        toast.error('Failed to add candidates to shortlist');
      }
      return;
    } else {
      const resp = await bulkDeleteMutateAsync({
        candidateIds: candidates.map((c) => c._id),
        shortlistId: list._id,
        orgId: org?._id as string,
      });

      if (resp) {
        toast.success('Candidates removed from shortlist');
        refetch();
        setSelectedCandidates([]);
        setOpenShortlist(false);
      } else {
        toast.error('Failed to remove candidates from shortlist');
      }
      return;
    }
  };

  const handleCreateShortlist = async (
    name: string,
    candidate?: ShortlistCandidate | null
  ) => {
    const resp = await createShortlistMutateAsync({
      candidateId: candidate?._id as string,
      orgId: org?._id as string,
      name,
    });

    if (resp) {
      toast.success('Shortlist created successfully');
      refetch();
      setOpenCreateShortlist(false);
    } else {
      toast.error('Failed to create shortlist');
    }
  };

  const handleCheckboxChange = (
    checked: boolean,
    candidate: ShortlistCandidate | string
  ) => {
    if (candidate === 'all') {
      if (checked) {
        setSelectedCandidates(data?.data || []);
      } else {
        setSelectedCandidates([]);
      }
      return;
    } else {
      setSelectedCandidates((prev) => {
        if (checked) {
          return [...prev, candidate as ShortlistCandidate];
        } else {
          return prev.filter(
            (c) => c._id !== (candidate as ShortlistCandidate)._id
          );
        }
      });
    }
  };

  useEffect(() => {
    const currentView = searchParams.get('view');
    if (currentView === 'shortlist') {
      setView('shortlist');
    } else if (currentView === 'candidate') {
      setView('candidate');
    }
    router.replace(window.location.pathname);
  }, [searchParams.get('view')]);

  return (
    <>
      <div className="flex flex-col gap-8">
        {/* <Breadcrumb
        items={[
          { name: 'All Jobs', href: '/employer/admin/jobs' },
          {
            name: 'Software Development Manager',
            href: '/jobs/software-development-manager',
          },
        ]}
      /> */}

        {/* <JobCard /> */}

        <div className="flex items-center justify-between gap-4">
          <h2 className="text-lg">Candidate list</h2>
          <Button
            className="bg-primary text-white"
            onClick={() => setView('shortlist')}
          >
            My Shortlist
          </Button>
        </div>

        <div className="flex flex-col gap-4">
          <CandidateFilter
            searchProps={{
              onChange: (e) => handleSearch(e.target.value),
              ref: searchRef,
            }}
            statusProps={{
              value: status,
              onClear: () => setStatus(null),
              onChange: handleChangeStatus,
            }}
            selectedCandidates={selectedCandidates}
            totalCandidates={data?.data?.length || 0}
            onHeartClick={() => setOpenShortlist(true)}
          />

          <CandidateTable
            jobCandidateData={data as ApiListResponse<ShortlistCandidate>}
            isLoading={isLoading}
            page={page}
            setPage={setPage}
            onClickAction={handleClickAction}
            onCheckboxChange={handleCheckboxChange}
            selectedCandidates={selectedCandidates}
          />
        </div>
      </div>

      {openShortlist && (
        <ShortlistModal
          open={openShortlist}
          onClose={() => setOpenShortlist(false)}
          shortlists={shortlists as Shortlist[]}
          onCheckboxChange={(checked, shortlist, candidate) =>
            selectedCandidates.length > 0
              ? handleCreateShortlistCandidates(
                  checked,
                  shortlist,
                  selectedCandidates
                )
              : handleCreateShortlistCandidate(
                  checked,
                  shortlist,
                  candidate as ShortlistCandidate
                )
          }
          candidate={candidateSeleted}
          candidates={selectedCandidates}
          isLoading={
            isDeleting || isCreating || isBulkCreating || isBulkDeleting
          }
          canUpdate={false}
          onCreate={() => {
            setOpenShortlist(false);
            setOpenCreateShortlist(true);
          }}
        />
      )}

      {openCreateShortlist && (
        <CreateShortlistModal
          open={openCreateShortlist}
          onClose={() => setOpenCreateShortlist(false)}
          candidate={candidateSeleted}
          isLoading={creatingShortlist}
          onCreate={handleCreateShortlist}
        />
      )}
    </>
  );
}
