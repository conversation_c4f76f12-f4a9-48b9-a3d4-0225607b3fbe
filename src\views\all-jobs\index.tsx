'use client';

import { use<PERSON>tom } from 'jotai';
import AllJobsFilter from './filter';
import AllJobsTable from './table';
import { userAtom } from '@/store/user-atom';
import { useListOrgJob } from '@/api-requests/job/get-org-jobs';
import { useRef, useState } from 'react';
import { Job } from '@/api-requests/job';
import { debounce } from 'lodash';
import { ApiListResponse, LIMIT } from '@/api-requests/types';

export interface Option {
  label: string;
  value: string;
}

export default function AllJobs() {
  const [user] = useAtom(userAtom);
  const [page, setPage] = useState(1);
  const [status, setStatus] = useState<Option | null>(null);
  const [search, setSearch] = useState<string>('');
  const searchRef = useRef<HTMLInputElement>(null!);

  const { data, isLoading, refetch } = useListOrgJob({
    userId: user?.id as string,
    page,
    limit: LIMIT,
    title: search,
    status: status?.value,
  });

  const handleChangeStatus = (option: Option | null) => {
    setStatus(option);
    setPage(1);
  };

  const handleSearch = debounce((value: string) => {
    if (!value || value.trim().length > 2) {
      setSearch(value);
      setPage(1);
    }
  }, 500);

  return (
    <div className="space-y-6">
      <AllJobsFilter
        searchProps={{
          // value: search,
          onChange: (e) => handleSearch(e.target.value),
          ref: searchRef,
        }}
        statusProps={{
          value: status,
          onClear: () => setStatus(null),
          onChange: handleChangeStatus,
        }}
      />

      <AllJobsTable
        jobData={data as ApiListResponse<Job>}
        isLoading={isLoading}
        page={page}
        setPage={setPage}
        refetch={refetch}
      />
    </div>
  );
}
