'use client';

import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useAtom } from 'jotai';
import {
  currentPageAtom,
  difficultyAtom,
  categoriesAtom,
  jobTypeAtom,
  resetFiltersAtom,
  salaryAtom,
} from '@/store/job-atom';
import { MultiSelect, MultiSelectOption, Checkbox, Input } from 'rizzui';
import debounce from 'lodash/debounce';
import cn from '@/utils/class-names';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useURLParamsManager } from '@/hooks/use-update-url-params';
import CloseIcon from '@/views/icons/close';

const BRAND = '#0D1321';

interface Option {
  label: string;
  value: string;
  description?: string;
}
interface ChipItem {
  label: string;
  value: string;
}

const jobTypeOptions: Option[] = [
  { label: 'Full Time', value: 'fulltime' },
  { label: 'Part Time', value: 'parttime' },
  { label: 'Contract', value: 'contract' },
  { label: 'Remote', value: 'remote' },
];

const difficultyOptions: Option[] = [
  { label: 'Beginner', value: '1' },
  { label: 'Intermediate', value: '2' },
  { label: 'Advanced', value: '3' },
];

const salaryOptions: Option[] = [
  { label: '$0 - $5,000', value: '0-5000' },
  { label: '$5,000 - $10,000', value: '5000-10000' },
  { label: '$10,000 - $15,000', value: '10000-15000' },
  { label: '$15,000 - $20,000', value: '15000-20000' },
  { label: '$20,000 - $25,000', value: '20000-25000' },
  { label: '$25,000 - $30,000', value: '25000-30000' },
  { label: '$30,000 - $35,000', value: '30000-35000' },
  { label: '$35,000 - $40,000', value: '35000-40000' },
  { label: '$40,000 - $45,000', value: '40000-45000' },
  { label: '$45,000 - $50,000', value: '45000-50000' },
  { label: '$50,000+', value: '50000+' },
];

// ---------- utilities ----------
const idsToChipItems = (ids: string[], list: Option[]): ChipItem[] =>
  ids
    .map((id) => {
      const o = list.find((x) => x.value === id);
      return o ? { label: o.label, value: o.value } : undefined;
    })
    .filter(Boolean) as ChipItem[];

function getOptionDisplayValue(option: MultiSelectOption, selected: boolean) {
  return (
    <div
      className="flex w-full cursor-pointer items-center justify-between gap-2 py-1"
      onClick={(e) => {
        e.currentTarget.dispatchEvent(
          new MouseEvent('mousedown', { bubbles: true })
        );
      }}
    >
      <div className="flex items-center gap-1">
        <span className="text-sm">{option.label}</span>
        {option.description && (
          <span className="text-xs text-gray-500">({option.description})</span>
        )}
      </div>
      <Checkbox
        variant="flat"
        size="sm"
        checked={selected}
        onClick={(e) => {
          e.stopPropagation();
          e.currentTarget.dispatchEvent(
            new MouseEvent('mousedown', { bubbles: true })
          );
        }}
        readOnly
      />
    </div>
  );
}

function displayValueCount(title: string, count: number) {
  return (
    <div className="flex w-full items-center truncate text-start">
      <span className="truncate">{title}</span>
      <span className="border-muted ms-2 border-s ps-2">{count} Selected</span>
    </div>
  );
}

function GroupedChips({
  title,
  items,
  onRemoveValue,
  onClear,
}: {
  title: string;
  items: ChipItem[];
  onRemoveValue: (value: string) => void;
  onClear: () => void;
}) {
  if (!items.length) return null;
  return (
    <div className="flex items-center gap-2 rounded-xl border border-slate-200 px-2 py-1">
      <span className="select-none whitespace-nowrap rounded-md bg-slate-100 px-2 py-0.5 text-xs font-medium text-slate-600">
        {title}
      </span>
      <div
        className="no-scrollbar flex max-w-full items-center gap-2 overflow-x-auto"
        style={{ scrollbarWidth: 'none' }}
      >
        {items.map((chip) => (
          <span
            key={chip.value}
            className="group inline-flex items-center gap-1 rounded-full border border-slate-200 bg-white/80 px-2 py-0.5 text-[12px] leading-5 shadow-sm"
          >
            <span className="max-w-[12rem] truncate">{chip.label}</span>
            <button
              onClick={() => onRemoveValue(chip.value)}
              className="-mr-1 rounded-full p-0.5 text-slate-400 hover:bg-slate-100 hover:text-slate-600"
              aria-label={`Remove ${chip.label}`}
            >
              <CloseIcon className="h-3 w-3" />
            </button>
          </span>
        ))}
      </div>
      <button
        onClick={onClear}
        className="ml-1 rounded-lg p-1 text-slate-400 hover:bg-slate-100 hover:text-slate-700"
        aria-label={`Clear ${title}`}
      >
        <CloseIcon className="h-4 w-4" />
      </button>
    </div>
  );
}

// --- BrandSlider (discrete 1..3), a11y + keyboard, brand fill ---
function BrandSlider({
  value,
  onChange,
  disabled,
}: {
  value: number;
  onChange: (v: number) => void;
  disabled?: boolean;
}) {
  const percent = ((Math.min(3, Math.max(1, value)) - 1) / 2) * 100; // 0, 50, 100
  return (
    <div
      className={cn(
        'brandRange relative w-full select-none',
        disabled && 'opacity-60'
      )}
    >
      <input
        type="range"
        min={1}
        max={3}
        step={1}
        aria-label="Difficulty"
        value={value}
        onChange={(e) => onChange(Number(e.target.value))}
        disabled={disabled}
        className="h-3 w-full cursor-pointer appearance-none rounded-full bg-transparent focus:outline-none"
        style={{
          // custom property consumed by CSS below to paint the filled track
          ['--percent' as any]: `${percent}%`,
          ['--brand' as any]: BRAND,
        }}
      />
      {/* <div className="mt-2 grid grid-cols-3 text-[12px] text-slate-500">
        <span className="text-left">Beginner</span>
        <span className="text-center">Intermediate</span>
        <span className="text-right">Advanced</span>
      </div> */}
      <style jsx>{`
        .brandRange input[type='range']::-webkit-slider-runnable-track {
          height: 8px;
          border-radius: 9999px;
          background: linear-gradient(
            to right,
            var(--brand) 0%,
            var(--brand) var(--percent),
            #e5e7eb var(--percent),
            #e5e7eb 100%
          );
        }
        .brandRange input[type='range']::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          height: 18px;
          width: 18px;
          border-radius: 9999px;
          background: var(--brand);
          border: 3px solid #fff;
          margin-top: -5px;
          box-shadow: 0 4px 12px rgba(13, 19, 33, 0.25);
        }
        .brandRange input[type='range']::-moz-range-track {
          height: 8px;
          border-radius: 9999px;
          background: linear-gradient(
            to right,
            var(--brand) 0%,
            var(--brand) var(--percent),
            #e5e7eb var(--percent),
            #e5e7eb 100%
          );
        }
        .brandRange input[type='range']::-moz-range-thumb {
          height: 18px;
          width: 18px;
          border-radius: 9999px;
          background: var(--brand);
          border: 3px solid #fff;
          box-shadow: 0 4px 12px rgba(13, 19, 33, 0.25);
        }
        .brandRange input[type='range']:focus-visible::-webkit-slider-thumb {
          outline: 3px solid rgba(13, 19, 33, 0.35);
          outline-offset: 2px;
        }
        .brandRange input[type='range']:focus-visible::-moz-range-thumb {
          outline: 3px solid rgba(13, 19, 33, 0.35);
          outline-offset: 2px;
        }
      `}</style>
    </div>
  );
}

// ============================ MAIN ============================
interface IProps {
  isHomePage?: boolean;
}

export default function JobFilter({ isHomePage }: IProps) {
  // atoms
  const [jobTypes, setJobTypes] = useAtom(jobTypeAtom);
  const [difficulty, setDifficulty] = useAtom(difficultyAtom); // array ['1'|'2'|'3']
  const [salary, setSalary] = useAtom(salaryAtom);
  const [categories, setCategories] = useAtom(categoriesAtom);
  const [, resetFilters] = useAtom(resetFiltersAtom);
  const [, setPage] = useAtom(currentPageAtom);

  // routing
  const searchInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();
  const pathname = usePathname();
  const urlSearchParams = useSearchParams();
  const urlManager = useURLParamsManager();

  // UI state
  const [mobileOpen] = useState(false); // (kept for possible reuse)
  const [difficultyEnabled, setDifficultyEnabled] = useState<boolean>(
    Boolean(
      urlSearchParams.get('level') ||
        (Array.isArray(difficulty) && difficulty.length)
    )
  );

  const totalSelected =
    jobTypes.length +
    salary.length +
    (categories ? 1 : 0) +
    (difficultyEnabled ? difficulty.length : 0);

  const controlClass =
    'w-full min-w-[240px] bg-transparent shadow-[0_1px_3px_rgba(0,0,0,0.05)] hover:shadow-[0_6px_18px_rgba(0,0,0,0.08)] transition-shadow [&>.rizzui-popover]:rounded-xl [&>.rizzui-popover]:shadow-md';

  const syncUrl = (key: string, value: string[] | string) => {
    urlManager.update({ [key]: value as any, page: 1 });
  };

  const handleFilterChange =
    (setter: (value: string[]) => void) =>
    (value: string[], paramKey: string) => {
      setter(value);
      setPage(1);
      syncUrl(paramKey, value);
    };

  const trueClearAll = () => {
    resetFilters();
    setJobTypes([]);
    setDifficulty([]);
    setSalary([]);
    setCategories('');
    setDifficultyEnabled(false);
    setPage(1);
    urlManager.clear();
    if (searchInputRef.current) searchInputRef.current.value = '';
  };

  const debouncedSetCategories = useRef(
    debounce((value: string) => {
      setCategories(value);
      setPage(1);
      syncUrl('categories', value);
    }, 500)
  ).current;
  useEffect(
    () => () => {
      debouncedSetCategories.cancel();
    },
    [debouncedSetCategories]
  );
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) =>
    debouncedSetCategories(e.target.value);

  // hydrate from URL
  useEffect(() => {
    const categories = urlSearchParams.get('categories') || '';
    const jobType = urlSearchParams.get('jobType') || '';
    const salary = urlSearchParams.get('salary') || '';
    const level = urlSearchParams.get('level') || '';
    const pageFromUrl = parseInt(urlSearchParams.get('page') || '1');
    setJobTypes(jobType ? jobType.split(',') : []);
    setDifficulty(level ? level.split(',') : []);
    setSalary(salary ? salary.split(',') : []);
    setCategories(categories);
    setPage(pageFromUrl || 1);
    if (level) setDifficultyEnabled(true);
  }, [
    urlSearchParams,
    setJobTypes,
    setDifficulty,
    setSalary,
    setCategories,
    setPage,
  ]);

  useEffect(() => {
    if (pathname === '/') {
      if (
        jobTypes.length ||
        salary.length ||
        categories ||
        (difficultyEnabled && difficulty.length)
      ) {
        router.push('/find-jobs');
      }
    }
  }, [
    pathname,
    jobTypes,
    salary,
    categories,
    difficultyEnabled,
    difficulty,
    router,
  ]);

  // chips
  const jobTypeItems = useMemo(
    () => idsToChipItems(jobTypes, jobTypeOptions),
    [jobTypes]
  );
  const salaryItems = useMemo(
    () => idsToChipItems(salary, salaryOptions),
    [salary]
  );
  const difficultyItems = useMemo(
    () =>
      difficultyEnabled ? idsToChipItems(difficulty, difficultyOptions) : [],
    [difficulty, difficultyEnabled]
  );
  const industryItems: ChipItem[] = categories
    ? [{ label: categories, value: categories }]
    : [];

  const counts: [string, number][] = [
    ['Job Type', jobTypes.length],
    ['Salary', salary.length],
    ['Industry', categories ? 1 : 0],
    ['Difficulty', difficultyEnabled ? difficulty.length : 0],
  ];

  // slider value (single discrete value)
  const difficultyNumeric = Number(difficulty?.[0] || 1);

  return isHomePage ? (
    <div className="grid grid-cols-1 items-center gap-4 md:grid-cols-[auto,1fr]">
      <div className="flex items-center gap-2">
        <Checkbox
          variant="flat"
          size="sm"
          checked={difficultyEnabled}
          onChange={(e) => {
            const next = (e.target as HTMLInputElement).checked;
            setDifficultyEnabled(next);
            if (next) {
              if (difficulty.length) {
                syncUrl('level', difficulty);
              } else {
                syncUrl('level', ['1']);
              }
            } else {
              syncUrl('level', []);
            }
          }}
          label={
            <span className="text-[14px] text-slate-700">
              Show tryable roles now
            </span>
          }
        />
      </div>

      <div className="flex max-w-[450px] items-center gap-3">
        <span className="text-[14px] font-medium text-slate-500">
          Difficulty
        </span>
        <BrandSlider
          value={difficultyNumeric}
          onChange={(v) => {
            const arr = [String(v)];
            setDifficulty(arr);
            setPage(1);
            if (difficultyEnabled) syncUrl('level', arr);
          }}
          disabled={!difficultyEnabled}
        />
        <span className="text-[14px]" style={{ color: BRAND }}>
          {difficultyEnabled
            ? difficultyNumeric === 1
              ? 'Beginner'
              : difficultyNumeric === 2
                ? 'Intermediate'
                : 'Advanced'
            : 'Beginner'}
        </span>

        <button
          className="whitespace-nowrap text-sm text-slate-600 underline hover:text-slate-900"
          onClick={trueClearAll}
        >
          Reset filters
        </button>
      </div>
    </div>
  ) : (
    <section className="w-full rounded-2xl border border-slate-200 bg-transparent p-4">
      <div className="mb-3 flex items-center justify-between gap-3">
        <h2 className="text-[18px] font-semibold text-slate-800">Filters</h2>
        {totalSelected > 0 && (
          <button
            className="text-sm text-slate-600 underline hover:text-slate-900"
            onClick={trueClearAll}
            aria-label="Clear all filters"
          >
            Clear all
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-3">
        <MultiSelect
          clearable
          value={jobTypes}
          options={jobTypeOptions}
          onChange={(val: string[]) =>
            handleFilterChange(setJobTypes)(val, 'jobType')
          }
          onClear={() => handleFilterChange(setJobTypes)([], 'jobType')}
          getOptionDisplayValue={getOptionDisplayValue}
          displayValue={(selected) =>
            displayValueCount('Job Type', selected.length)
          }
          placeholder="Job Type"
          className={controlClass}
        />
        <MultiSelect
          clearable
          value={salary}
          options={salaryOptions}
          onChange={(val: string[]) =>
            handleFilterChange(setSalary)(val, 'salary')
          }
          onClear={() => handleFilterChange(setSalary)([], 'salary')}
          getOptionDisplayValue={getOptionDisplayValue}
          displayValue={(selected) =>
            displayValueCount('Salary Range', selected.length)
          }
          placeholder="Salary Range"
          className={controlClass}
        />
        <Input
          ref={searchInputRef}
          placeholder="Search by Industry"
          className="w-full min-w-[240px]"
          defaultValue={categories}
          onChange={handleSearchChange}
        />
      </div>

      <div className="grid grid-cols-1 items-center gap-4 pt-5 md:grid-cols-[auto,1fr]">
        <div className="flex items-center gap-2">
          <Checkbox
            variant="flat"
            size="sm"
            checked={difficultyEnabled}
            onChange={(e) => {
              const next = (e.target as HTMLInputElement).checked;
              setDifficultyEnabled(next);
              if (next) {
                if (difficulty.length) {
                  syncUrl('level', difficulty);
                } else {
                  syncUrl('level', ['1']);
                }
              } else {
                syncUrl('level', []);
              }
            }}
            label={
              <span className="text-[14px] text-slate-700">
                Show tryable roles now
              </span>
            }
          />
        </div>

        <div className="flex max-w-[400px] items-center gap-3">
          <span className="text-[14px] font-medium text-slate-500">
            Difficulty
          </span>
          <BrandSlider
            value={difficultyNumeric}
            onChange={(v) => {
              const arr = [String(v)];
              setDifficulty(arr);
              setPage(1);
              if (difficultyEnabled) syncUrl('level', arr);
            }}
            disabled={!difficultyEnabled}
          />
          <span className="text-[14px]" style={{ color: BRAND }}>
            {difficultyEnabled
              ? difficultyNumeric === 1
                ? 'Beginner'
                : difficultyNumeric === 2
                  ? 'Intermediate'
                  : 'Advanced'
              : 'Beginner'}
          </span>
        </div>
      </div>

      {totalSelected > 0 && <hr className="my-5 border-slate-200" />}

      {totalSelected > 0 && (
        <div className="flex flex-col gap-4">
          <GroupedChips
            title="Job Type"
            items={jobTypeItems}
            onRemoveValue={(value) => {
              const next = jobTypes.filter((v) => v !== value);
              setJobTypes(next);
              setPage(1);
              syncUrl('jobType', next);
            }}
            onClear={() => {
              setJobTypes([]);
              setPage(1);
              syncUrl('jobType', []);
            }}
          />
          <GroupedChips
            title="Salary Range"
            items={salaryItems}
            onRemoveValue={(value) => {
              const next = salary.filter((v) => v !== value);
              setSalary(next);
              setPage(1);
              syncUrl('salary', next);
            }}
            onClear={() => {
              setSalary([]);
              setPage(1);
              syncUrl('salary', []);
            }}
          />
          <GroupedChips
            title="Industry"
            items={industryItems}
            onRemoveValue={() => {
              setCategories('');
              setPage(1);
              syncUrl('categories', '');
            }}
            onClear={() => {
              setCategories('');
              setPage(1);
              syncUrl('categories', '');
            }}
          />
          {difficultyEnabled && (
            <GroupedChips
              title="Difficulty"
              items={difficultyItems}
              onRemoveValue={(value) => {
                const next = difficulty.filter((v) => v !== value);
                setDifficulty(next);
                setPage(1);
                syncUrl('level', next);
              }}
              onClear={() => {
                setDifficulty([]);
                setPage(1);
                syncUrl('level', []);
              }}
            />
          )}
        </div>
      )}
    </section>
  );
}
