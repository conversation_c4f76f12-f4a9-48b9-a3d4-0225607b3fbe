'use client';

import dynamic from 'next/dynamic';
import HeroTrial from './hero-trial';
import { usePathname } from 'next/navigation';
import JobSuggestion from './job-suggestion';

const JobSearch = dynamic(() => import('./job-search'), {
  ssr: false,
});
const JobFilter = dynamic(() => import('./job-filter'), {
  ssr: false,
});

export default function JobBanner() {
  const pathname = usePathname();
  const isHomePage = pathname === '/';

  return (
    <>
      {isHomePage && (
        <section
          className="relative bg-cover bg-center bg-no-repeat p-4 xl:px-0"
          style={{ backgroundImage: 'url("/job/fast-feedback.png")' }}
        >
          <div className="mx-auto mb-8 mt-10 flex max-w-[1440px] flex-col gap-6">
            <HeroTrial />
          </div>
        </section>
      )}

      <div className="relative w-full bg-[##F8F8F8]">
        <div
          className="bg-bottom bg-no-repeat"
          style={{
            backgroundImage: `linear-gradient(to bottom, #F8F8F8 calc(100% - 500px), transparent calc(100% - 500px)), url("/job/job-hero.png")`,
          }}
        >
          <div className="mx-auto flex max-w-[1440px] flex-col gap-6 p-4 py-12 xl:py-20 xl:px-0">
            {/* <div>
              <h1 className="mb-2 text-3xl font-bold text-primary md:text-4xl">
                Try the Job Before You Apply
              </h1>
              <p className="mb mx-auto text-base text-gray-600 md:text-lg">
                No more ghosted applications. Land better jobs through job
                simulations.
              </p>
            </div> */}

            <div className="space-y-4">
              <JobSearch />

              <JobFilter isHomePage={isHomePage} />
            </div>

            {isHomePage && <JobSuggestion />}
          </div>
        </div>
      </div>
    </>
  );
}
