'use client';

import { useAtom } from 'jotai';
import DetailIcon from '../icons/detail';
import InfoIcon from '../icons/info';
import SummaryIcon from '../icons/summary';
import { jobCreationStepAtom } from '@/store/job-creation-atom';
import { useEffect } from 'react';

const steps = [
  {
    title: 'Classify',
    step: 1,
    icon: <InfoIcon className="h-5 w-5" />,
  },
  {
    title: 'Add Detail',
    step: 2,
    icon: <DetailIcon className="h-5 w-5" />,
  },
  {
    title: 'Summary',
    step: 3,
    icon: <SummaryIcon className="h-5 w-5" />,
  },
];

export default function Stepper() {
  const [step, setStep] = useAtom(jobCreationStepAtom);

  const isActive = (s: number) => s <= step;

  const handleStepChange = (s: number) => {
    setStep(s);
  };

  useEffect(() => {
    const hash = window.location.hash;
    if (hash.startsWith('#step-')) {
      const stepFromHash = parseInt(hash.replace('#step-', ''));
      if (stepFromHash >= 1 && stepFromHash <= 3) {
        setStep(stepFromHash);
      }
    }

    return () => {
      setStep(1);
    };
  }, [setStep]);

  useEffect(() => {
    window.history.pushState(
      {},
      '',
      `${window.location.pathname}#step-${step}`
    );
  }, [step]);

  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-3 md:gap-6">
      {steps.map((s) => (
        <div
          key={s.step}
          className={`group flex cursor-pointer items-center gap-4 rounded-full px-4 py-3 transition-all duration-200 hover:shadow-xl ${
            isActive(s.step)
              ? 'bg-white shadow-lg'
              : 'bg-white/60 text-gray-400 shadow-md'
          }`}
          onClick={() => handleStepChange(s.step)}
        >
          <div
            className={`flex h-9 w-9 items-center justify-center rounded-full border group-hover:bg-primary group-hover:text-white ${
              isActive(s.step)
                ? 'bg-primary text-white'
                : 'border-gray-300 bg-white text-gray-400'
            }`}
          >
            {s.icon}
          </div>
          <div className="leading-tight">
            <p
              className={`text-xs group-hover:text-gray-500 ${isActive(s.step) ? 'text-gray-500' : 'text-gray-400'}`}
            >
              Step {s.step}
            </p>
            <p
              className={`text-sm font-medium group-hover:text-black ${isActive(s.step) ? 'text-black' : 'text-gray-500'}`}
            >
              {s.title}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
}
