import {
  ProcessCVToApplyResponse,
  useApplyCV,
  useProcessCVToApply,
} from '@/api-requests/job/process-cv-to-apply';

import StartSimulationButton from '@/components/StartSimulationButton';
import { userAtom } from '@/store/user-atom';
import { getUserError } from '@/utils/api-error';
import cn from '@/utils/class-names';
import CloseIcon from '@/views/icons/close';
import { AnimatePresence, motion } from 'framer-motion';
import { useAtom } from 'jotai';
import {
  ArrowRight,
  Check,
  CheckCircle2,
  Eye,
  Loader2,
  RotateCcw,
  Sparkles,
  Upload,
  User,
} from 'lucide-react';
import Link from 'next/link';
import { useEffect, useRef, useState } from 'react';
import toast from 'react-hot-toast';

import { Button, Modal, Progressbar } from 'rizzui';

interface ICVApplyEnhancedModalProps {
  jobId: string;
  simulationId: string;
  open: boolean;
  setOpen: (open: boolean) => void;
  onApplySuccess: () => void;
  onErrorJobApplied?: () => void;
}

interface CVSelectionData {
  type: 'upload' | 'profile' | null;
  file: File | null;
}

interface QuestionSubmission {
  id: string;
  answer: "yes" | "no" | number | string | string[];
}

const DELAY_PROGRESS = 1700;

const StepDot = ({ active, done }: { active?: boolean; done?: boolean }) => (
  <div
    className={[
      'grid h-6 w-6 place-items-center rounded-full text-xs font-semibold',
      done
        ? 'bg-emerald-600 text-white'
        : active
          ? 'bg-slate-900 text-white'
          : 'bg-slate-200 text-slate-600',
    ].join(' ')}
  >
    {done ? <Check className="h-4 w-4" /> : null}
  </div>
);

const Step = ({
  title,
  index,
  current,
}: {
  title: string;
  index: number;
  current: number;
}) => {
  const done = current > index;
  const active = current === index;
  return (
    <div className="flex items-center gap-2">
      <StepDot active={active} done={done} />
      <div
        className={
          'text-sm font-medium ' +
          (active
            ? 'text-slate-900'
            : done
              ? 'text-slate-700'
              : 'text-slate-500')
        }
      >
        {title}
      </div>
    </div>
  );
};

const PrimaryBtn = (props: any) => (
  <Button
    {...props}
    className={cn(
      'transition-all',
      props.disabled
        ? 'bg-primary/70 text-white'
        : 'bg-primary text-white hover:bg-primary/80'
    )}
  />
);

const SecondaryBtn = (props: any) => (
  <Button
    {...props}
    variant="outline"
    className={cn(
      'transition-all',
      props.selected
        ? 'border-2 border-slate-900 bg-slate-50'
        : 'border-slate-300 text-slate-800 hover:bg-slate-50'
    )}
  />
);



const CVSelectionStep = ({
  onNext,
}: {
  onNext: (data: {
    choice: 'upload' | 'profile' | null;
    file: File | null;
  }) => void;
}) => {
  const [user] = useAtom(userAtom);

  const [choice, setChoice] = useState<'upload' | 'profile' | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);

  const validateChoosenFile = (file?: File) => {
    if (!file) {
      toast.error('Please select a CV file');
      return false;
    }
    // Check file type
    const allowedTypes = [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Please select a PDF or DOCX file');
      return false;
    }

    // Check file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast.error('File size must be less than 2MB');
      return false;
    }
    return true;
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    console.log('handleFileSelect ::: ', event);
    const file = event.target.files?.[0];
    if (validateChoosenFile(file)) {
      setSelectedFile(file!);
      setChoice('upload');
    } else {
      console.log('File not valid');
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const file = e.dataTransfer.files?.[0];
    if (validateChoosenFile(file)) {
      setSelectedFile(file);
      setChoice('upload');
    }
  };

  return (
    <div>
      <div className="mb-4 text-sm text-slate-600">
        Choose how you want to apply for this position.
      </div>
      <div className="flex w-full flex-row flex-wrap justify-center gap-3">
        {/* Upload CV Option */}
        <div
          className={cn(
            'flex min-w-[300px] max-w-[600px] flex-1 cursor-pointer rounded-lg border-2 border-dashed p-6 shadow-sm transition-colors hover:border-primary hover:shadow',
            choice === 'upload' && 'border-primary'
          )}
          onClick={() => setChoice('upload')}
        >
          <div
            className={cn(
              'cursor-pointer',
              dragActive && 'border-primary bg-primary/5'
            )}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <input
              type="file"
              accept=".pdf,.docx"
              onChange={handleFileSelect}
              className="hidden"
              id="cv-upload"
            />
            <label htmlFor="cv-upload" className="cursor-pointer">
              <div className="flex flex-row items-center gap-3">
                <div className="flex min-h-10 min-w-10 items-center justify-center rounded-lg bg-primary/10">
                  <Upload className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="font-medium text-gray-700">Upload New CV</p>
                  <p className="text-sm text-gray-500">
                    {selectedFile ? selectedFile.name : 'PDF or DOCX, max 2MB'}
                  </p>
                </div>
              </div>
            </label>
          </div>
        </div>

        {/* Use Profile CV Option */}
        {user?.profile?.cv && (
          <div
            className={cn(
              'flex min-w-[300px] max-w-[600px] flex-1 cursor-pointer rounded-lg border-2 p-6 shadow-sm transition-colors hover:shadow',
              choice === 'profile' && 'border-primary'
            )}
            onClick={() => setChoice('profile')}
          >
            <div className="flex flex-row items-center gap-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                <User className="h-5 w-5 text-primary" />
              </div>
              <div>
                <p className="font-medium text-gray-700">Use Profile CV</p>
                <p className="text-sm text-gray-500">
                  Use the CV from your profile
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
      <div className="mt-4 flex justify-end">
        <PrimaryBtn
          disabled={!choice || (choice === 'upload' && !selectedFile)}
          onClick={() => onNext({ choice, file: selectedFile })}
        >
          Next <ArrowRight className="h-4 w-4" />
        </PrimaryBtn>
      </div>
    </div>
  );
};

const CVProcessingStep = ({
  jobId,
  simulationId,
  cvData,
  onSuccess,
  onAnalyzing,
  onErrorJobApplied,
}: {
  jobId: string;
  simulationId: string;
  cvData: CVSelectionData;
  onSuccess: (result: ProcessCVToApplyResponse | null) => void;
  onAnalyzing: (value: boolean) => void;
  onErrorJobApplied?: () => void;
}) => {
  const [progress, setProgress] = useState(0);
  const hasCalledRef = useRef(false);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const { mutateAsync: processCV } = useProcessCVToApply();
  const [showRetryButton, setShowRetryButton] = useState(false);

  const startProgress = () => {
    const duration = 6000;
    const target = 90;
    const stepTime = 100; // ms
    const step = (target / duration) * stepTime;
    setProgress(0);

    progressIntervalRef.current = setInterval(() => {
      setProgress((prev) => {
        if (prev + step >= target) {
          if (progressIntervalRef.current)
            clearInterval(progressIntervalRef.current);
          return target;
        }
        return prev + step;
      });
    }, stepTime);
  };

  const startProcessingCV = async () => {
    startProgress();
    onAnalyzing(true);
    setShowRetryButton(false);
    try {
      const res = await processCV({
        jobId,
        simulationId,
        cvFile:
          cvData.type === 'upload' && cvData.file ? cvData.file : undefined,
        useProfileCV: cvData.type === 'profile',
      });
      setTimeout(() => {
        onSuccess?.(res);
        onAnalyzing(false);
      }, DELAY_PROGRESS);
    } catch (error: any) {
      const userError = getUserError(
        error,
        'Failed to process CV. Please try again later.'
      );
      setTimeout(() => {
        userError.code === 'JOB_APPLIED'
          ? toast.success(userError.message)
          : toast.error(userError.message);
        if (userError.code === 'JOB_APPLIED' && !!onErrorJobApplied) {
          onErrorJobApplied?.();
        } else setShowRetryButton(true);
        onAnalyzing(false);
      }, DELAY_PROGRESS);
    }

    setTimeout(() => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }
      setProgress(100);
    }, 1500);
  };

  useEffect(() => {
    if (hasCalledRef.current) return;

    hasCalledRef.current = true;
    startProcessingCV();
  }, []);

  return (
    <div className="w-full">
      {!showRetryButton && <Loader2 className="m-auto h-8 w-8 animate-spin" />}
      <div className="mt-4 w-full text-center">
        <div className="text-sm font-medium">
          {!showRetryButton
            ? `Analyzing CV ${Math.round(progress)}%`
            : 'Oops! Something went wrong. Please try again.'}
        </div>
        <div className="w-full rounded">
          {!showRetryButton ? (
            <>
              <Progressbar
                value={progress}
                trackClassName="bg-gray-200 [&>div]:transition-all [&>div]:duration-100"
                labelClassName="hidden"
              />
            </>
          ) : (
            <Button className="mt-3 text-white" onClick={startProcessingCV}>
              Try again
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

const CVProcessingResultStep = ({
  result,
  onSuccess,
  onTryQuestions,
  onErrorJobApplied,
}: {
  result: ProcessCVToApplyResponse;
  onSuccess: () => void;
  onTryQuestions: () => void;
  onErrorJobApplied?: () => void;
}) => {
  const aiOverview = [...(result.overview || []), ...(result.feedback || [])];

  const { mutateAsync: applyCV, isPending } = useApplyCV();

  const handleApplyCV = async () => {
    if (!result) return;

    try {
      await applyCV({
        processId: result.id,
        tasks: undefined,
        quickQuestions: undefined,
      });
      toast.success('Successfully applied for the job.');
      onSuccess?.();
    } catch (error: any) {
      const userError = getUserError(
        error,
        'Failed to apply for the job. Please try again later.'
      );
      toast.error(userError.message);

      if (userError.code === 'JOB_APPLIED') {
        onErrorJobApplied?.();
      }
    }
  };

  const potentialMatchPercentage = result.matchPercentage + 20;
  const simulationMatchPercentage = 100;

  return (
    <div className="w-full">
      <div className="mb-6">
        <div className="text-sm text-slate-600 mb-2">Resume Match</div>
        <div className="text-4xl font-bold mb-2">{result.matchPercentage}%</div>
        <div className="text-sm text-slate-600 mb-4">
          <div className="flex items-center gap-2 mb-1">
            <span>• Improve +20% with questions</span>
          </div>
          <div className="flex items-center gap-2">
            <span>• Reach up to 100% with Simulation</span>
          </div>
        </div>
        <div className="w-full bg-slate-200 rounded-full h-2 mb-6">
          <div
            className="bg-slate-900 h-2 rounded-full transition-all duration-500"
            style={{ width: `${result.matchPercentage}%` }}
          ></div>
        </div>
      </div>

      <div className="grid gap-4 sm:grid-cols-2 mb-6">
        <div className="rounded-xl border p-4">
          <div className="flex items-center gap-2 text-sm font-semibold mb-3">
            <Sparkles className="h-4 w-4" /> AI Overview
          </div>
          <ul className="list-inside list-disc space-y-1 text-sm text-slate-600">
            {aiOverview.map((f, i) => (
              <li key={i}>{f}</li>
            ))}
          </ul>
        </div>
        <div className="space-y-3">
          {!!result?.quickQuestions?.length && (
            <button
              onClick={onTryQuestions}
              className="w-full flex items-center justify-between p-4 rounded-xl border-2 border-slate-900 bg-slate-50 hover:bg-slate-100 transition-colors"
            >
              <div className="text-left">
                <div className="font-semibold text-slate-900">Answer Questions (→ {potentialMatchPercentage}%)</div>
                <div className="text-sm text-slate-600">Quick fit questions to boost your profile</div>
              </div>
              <ArrowRight className="h-5 w-5 text-slate-900" />
            </button>
          )}

          <button className="w-full flex items-center justify-between p-4 rounded-xl border border-slate-300 hover:bg-slate-50 transition-colors">
            <div className="text-left">
              <div className="font-semibold text-slate-900">Try Simulation (→ {simulationMatchPercentage}%)</div>
              <div className="text-sm text-slate-600">Complete job simulation for best results</div>
            </div>
            <ArrowRight className="h-5 w-5 text-slate-600" />
          </button>

          <button
            onClick={handleApplyCV}
            disabled={isPending}
            className="w-full p-4 rounded-xl bg-white border border-slate-300 hover:bg-slate-50 transition-colors disabled:opacity-50"
          >
            <div className="font-semibold text-slate-900">Submit Anyway ({result.matchPercentage}%)</div>
          </button>
        </div>
      </div>
    </div>
  );
};

const CVOptionalQuestionsStep = ({
  result,
  simulationId,
  onSuccess,
  onBackToResult,
  onErrorJobApplied,
}: {
  result: ProcessCVToApplyResponse;
  simulationId: string;
  onSuccess: () => void;
  onBackToResult: () => void;
  onErrorJobApplied?: () => void;
}) => {
  const [questionSubmissions, setQuestionSubmissions] = useState<QuestionSubmission[]>(
    result.quickQuestions?.map((question) => ({
      id: question.id,
      answer: question.type === 'yes_no' ? 'yes' : question.type === 'number' ? 0 : question.type === 'multiple' ? [] : '',
    })) || []
  );

  const updateQuestionAnswer = (questionId: string, answer: any) => {
    setQuestionSubmissions(prev =>
      prev.map(submission =>
        submission.id === questionId
          ? { ...submission, answer }
          : submission
      )
    );
  };

  const { mutateAsync: applyCV, isPending } = useApplyCV();

  const validateRequiredQuestions = () => {
    const requiredQuestions = result.quickQuestions?.filter(q => q.required) || [];
    const missingAnswers = requiredQuestions.filter(question => {
      const submission = questionSubmissions.find(s => s.id === question.id);
      if (!submission) return true;

      if (question.type === 'multiple') {
        return !Array.isArray(submission.answer) || submission.answer.length === 0;
      }

      return submission.answer === '' || submission.answer === null || submission.answer === undefined;
    });

    return missingAnswers;
  };

  const handleApplyCV = async () => {
    if (!result) return;

    const missingAnswers = validateRequiredQuestions();
    if (missingAnswers.length > 0) {
      toast.error(`Please answer all required questions: ${missingAnswers.map(q => q.text).join(', ')}`);
      return;
    }

    // Prepare quickQuestions with full object structure for API
    const quickQuestionsForAPI = result.quickQuestions?.map(question => {
      const submission = questionSubmissions.find(s => s.id === question.id);
      return {
        ...question,
        answer: submission?.answer
      };
    });

    try {
      await applyCV({
        processId: result.id,
        tasks: undefined,
        quickQuestions: quickQuestionsForAPI && quickQuestionsForAPI.length > 0 ? quickQuestionsForAPI : undefined,
      });
      toast.success('Successfully applied for the job.');
      onSuccess?.();
    } catch (error: any) {
      const userError = getUserError(
        error,
        'Failed to apply for the job. Please try again later.'
      );
      toast.error(userError.message);

      if (userError.code === 'JOB_APPLIED') {
        onErrorJobApplied?.();
      }
    }
  };

  const renderQuestionInput = (question: any, submission: QuestionSubmission) => {
    switch (question.type) {
      case 'yes_no':
        return (
          <div className="space-y-2">
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name={`question-${question.id}`}
                value="yes"
                checked={submission.answer === 'yes'}
                onChange={(e) => updateQuestionAnswer(question.id, e.target.value)}
                className="text-primary focus:ring-primary"
              />
              <span>Yes</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name={`question-${question.id}`}
                value="no"
                checked={submission.answer === 'no'}
                onChange={(e) => updateQuestionAnswer(question.id, e.target.value)}
                className="text-primary focus:ring-primary"
              />
              <span>No</span>
            </label>
          </div>
        );

      case 'number':
        return (
          <input
            type="number"
            value={submission.answer as number}
            onChange={(e) => updateQuestionAnswer(question.id, Number(e.target.value))}
            className="w-full rounded border border-slate-300 p-3 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
            placeholder="Enter a number..."
          />
        );

      case 'single':
        return (
          <div className="space-y-2">
            {question.options?.map((option: string, index: number) => (
              <label key={index} className="flex items-center space-x-2">
                <input
                  type="radio"
                  name={`question-${question.id}`}
                  value={option}
                  checked={submission.answer === option}
                  onChange={(e) => updateQuestionAnswer(question.id, e.target.value)}
                  className="text-primary focus:ring-primary"
                />
                <span>{option}</span>
              </label>
            ))}
          </div>
        );

      case 'multiple':
        return (
          <div className="space-y-2">
            {question.options?.map((option: string, index: number) => (
              <label key={index} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  value={option}
                  checked={Array.isArray(submission.answer) && submission.answer.includes(option)}
                  onChange={(e) => {
                    const currentAnswers = Array.isArray(submission.answer) ? submission.answer : [];
                    if (e.target.checked) {
                      updateQuestionAnswer(question.id, [...currentAnswers, option]);
                    } else {
                      updateQuestionAnswer(question.id, currentAnswers.filter(a => a !== option));
                    }
                  }}
                  className="text-primary focus:ring-primary"
                />
                <span>{option}</span>
              </label>
            ))}
          </div>
        );

      case 'text':
      default:
        return (
          <textarea
            value={submission.answer as string}
            onChange={(e) => updateQuestionAnswer(question.id, e.target.value)}
            className="w-full h-24 rounded border border-slate-300 p-3 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary resize-none"
            placeholder="Enter your answer..."
          />
        );
    }
  };

  return (
    <div className="w-full">
      <div className="mb-6 flex justify-between">
        <div>
          <div className="text-lg font-semibold">Quick Fit Questions</div>
          <div className="text-sm text-slate-600">
            Answer these questions to boost your profile visibility.
          </div>
        </div>
        <SecondaryBtn onClick={onBackToResult}>Skip Questions</SecondaryBtn>
      </div>

      <div className="space-y-6">
        {result.quickQuestions?.map((question, index) => {
          const submission = questionSubmissions.find(s => s.id === question.id);
          if (!submission) return null;

          return (
            <motion.div
              key={question.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="rounded-xl border border-slate-200 p-6"
            >
              <div className="mb-4">
                <div className="flex items-center gap-2 mb-2">
                  <span className="font-semibold text-slate-900">
                    {question.text}
                  </span>
                  {question.required && (
                    <span className="text-red-500 text-sm">*</span>
                  )}
                </div>
                <div className="text-sm text-slate-600">
                  Question {index + 1} of {result.quickQuestions?.length}
                </div>
              </div>

              {renderQuestionInput(question, submission)}
            </motion.div>
          );
        })}
      </div>

      <div className="mt-8 flex flex-wrap justify-end gap-3">
        <StartSimulationButton
          simId={simulationId}
          buttonProps={{
            variant: 'outline',
            disabled: isPending,
          }}
        >
          Try the Job Simulation
        </StartSimulationButton>
        <PrimaryBtn onClick={handleApplyCV} disabled={isPending}>
          {isPending ? 'Submitting...' : 'Submit Application'}
        </PrimaryBtn>
      </div>
    </div>
  );
};

export default function CVApplyEnhancedModal({
  open,
  jobId,
  simulationId,
  setOpen,
  onApplySuccess,
  onErrorJobApplied,
}: ICVApplyEnhancedModalProps) {
  const [user] = useAtom(userAtom);

  const [step, setStep] = useState(1);
  const [cvData, setCvData] = useState<CVSelectionData | null>(null);
  const [result, setResult] = useState<ProcessCVToApplyResponse | null>(null);
  const [submitted, setSubmitted] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const getModalClassName = () => {
    // if (step === 0) {
    //   return 'md:w-[800px] md:max-w-[800px]';
    // }
    // if (step === 1) {
    //   return 'md:w-[600px] md:max-w-[600px]';
    // }
    return 'md:w-[1000px] md:max-w-[1000px]';
  };

  const reset = () => {
    if (step === 2 && isAnalyzing) return;
    console.log('RESET ...');
    setStep(1);
    setSubmitted(false);
    setCvData(null);
  };

  const close = () => {
    setOpen(false);
  };

  const handleProcessCVSuccess = (result: ProcessCVToApplyResponse | null) => {
    if (result && step === 2) {
      console.log('handleProcessCVSuccess ::: ', step, result);
      setResult(result);
      setStep(3);
    }
  };

  const handleSubmitSuccess = () => {
    setSubmitted(true);
    onApplySuccess?.();
  };

  return (
    <>
      <Modal
        isOpen={open}
        onClose={() => setOpen(false)}
        containerClassName={getModalClassName()}
      >
        <div className="flex flex-col gap-6 p-8">
          <div className="flex flex-row items-center justify-between">
            <p className="text-lg font-bold">Apply with CV</p>
            <CloseIcon className="cursor-pointer" onClick={close} />
          </div>

          {submitted ? (
            <motion.div
              className="mt-4 flex flex-col items-center gap-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              <CheckCircle2 className="h-16 w-16 text-emerald-600" />
              <div className="text-lg font-semibold">
                Your application has been submitted successfully.
                <br />
              </div>
              <div className="mt-4 flex gap-2">
                <PrimaryBtn>
                  <Link href={`/profile/${user?.id}#applications`}>
                    View My Applications
                  </Link>
                </PrimaryBtn>
              </div>
            </motion.div>
          ) : (
            <>
              <div className="mb-6 mt-2 grid grid-cols-5 gap-2">
                <Step title="Choose CV" index={1} current={step} />
                <Step title="Analyzing" index={2} current={step} />
                <Step title="Match Result" index={3} current={step} />
                <Step title="Questions" index={4} current={step} />
                <button
                  onClick={reset}
                  className="ml-auto flex items-center gap-1 text-xs text-slate-500 hover:text-slate-900"
                >
                  <RotateCcw className="h-4 w-4" /> Reset
                </button>
              </div>

              <AnimatePresence mode="wait">
                {step === 1 && (
                  <motion.div
                    key="s1"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                  >
                    <CVSelectionStep
                      onNext={(data) => {
                        setCvData({
                          type: data.choice,
                          file: data.file,
                        });
                        setStep(2);
                      }}
                    />
                  </motion.div>
                )}

                {step === 2 && !!cvData && (
                  <motion.div
                    key="s2"
                    className="flex flex-col items-center gap-4 py-10"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                  >
                    <CVProcessingStep
                      jobId={jobId}
                      simulationId={simulationId}
                      cvData={cvData}
                      onSuccess={handleProcessCVSuccess}
                      onAnalyzing={(value) => setIsAnalyzing(value)}
                      onErrorJobApplied={onErrorJobApplied}
                    />
                  </motion.div>
                )}

                {step === 3 && !!result && (
                  <motion.div
                    key="s3"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="flex flex-col gap-4"
                  >
                    <CVProcessingResultStep
                      result={result}
                      onTryQuestions={() => setStep(4)}
                      onSuccess={handleSubmitSuccess}
                      onErrorJobApplied={onErrorJobApplied}
                    />
                  </motion.div>
                )}

                {step === 4 && !!result?.quickQuestions?.length && (
                  <motion.div
                    key="s4"
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -50 }}
                  >
                    <CVOptionalQuestionsStep
                      result={result}
                      simulationId={simulationId}
                      onSuccess={handleSubmitSuccess}
                      onErrorJobApplied={onErrorJobApplied}
                      onBackToResult={() => setStep(3)}
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </>
          )}
        </div>
      </Modal>
    </>
  );
}
