/* @import "tailwindcss"; */

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
  /* --primary: #0D1321 */
  --primary-rgb: 27 28 30;
  --primary: rgb(var(--primary-rgb));
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* 
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}
*/

[data-theme='dark'],
.dark,
:root.dark,
:root[data-theme='dark'] {
  --background: #ffffff !important;
  --foreground: #171717 !important;
  color-scheme: light !important;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* .text-primary {
  color: var(--primary);
}

.bg-primary {
  background-color: var(--primary);
}

.border-primary {
  border-color: var(--primary);
} */

/* Checkbox styles */
.rizzui-checkbox-input {
  background-color: #ffffff;
  border: 1px solid #d1d5db;
}

.rizzui-checkbox-input:checked {
  background-color: #ffffff;
  /* border: 1px solid var(--primary); */
  border: 1px solid #d1d5db;
}

.rizzui-checkbox-input:checked + .rizzui-checkbox-icon {
  color: var(--primary);
  stroke: var(--primary);
  background-color: #ffffff;
  top: 0;
  stroke-width: 3px;
  border: 1px solid #d1d5db;
}

.rizzui-multi-select-button,
.rizzui-input-container {
  border-color: #b4b4b4;
  background: #ffffff;
  border-width: 1px;
}

.rizzui-multi-select-button:hover,
.rizzui-input-container:hover,
.rizzui-multi-select-button:focus,
.rizzui-input-container:focus {
  border-color: #b4b4b4;
  outline-color: #b4b4b4;
}

/* .rizzui-multi-select-options {
  background: #ffffff;
  border-color: none;
} */

.rizzui-radio-field {
  box-shadow: none;
}

.rizzui-modal-container,
.rizzui-dropdown-menu,
.rizzui-select-options,
.rizzui-multi-select-options {
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.15);
  background-color: white;
}

.react-datepicker-wrapper {
  width: 100%;
}

.rizzui-switch-knob {
  background-color: var(--primary);
}

.rizzui-switch input[disabled] + span .rizzui-switch-knob,
.rizzui-switch input:disabled + span .rizzui-switch-knob {
  background-color: #868990;
}

.rizzui-switch span[class*='bg-muted'] {
  background-color: #e3e3e3;
}

.rizzui-switch input:checked + span {
  background-color: #e3e3e3;
}

.rizzui-radio-field {
  box-shadow: none;
  position: relative;
  border-color: var(--primary);
}

.rizzui-radio-field:checked {
  border: 1px solid var(--primary);
}

.rizzui-radio-field:checked::after {
  content: '';
  position: absolute;
  top: 4px;
  left: 4px;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border-radius: 50%;
  background: var(--primary);
  display: block;
}
