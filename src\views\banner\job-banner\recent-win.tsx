'use client';

import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import { Avatar } from 'rizzui';

const ALL_WINS = [
  {
    name: '<PERSON><PERSON>',
    src: '/employer/avatar-albert.jpeg',
    description:
      'completed a 7-minute task and received an interview in 3 days.',
  },
  {
    name: '<PERSON>',
    src: '/employer/avatar-arlene.jpeg',
    description: 'shared his score and booked a manager chat the same week.',
  },
  {
    name: '<PERSON><PERSON>',
    src: '/employer/avatar-cody.jpeg',
    description: 'improved her score by 12% after feedback and got an offer.',
  },
  {
    name: '<PERSON>',
    src: '/employer/avatar-jacob.jpeg',
    description:
      'switched roles after showcasing a product task and doubled his pay.',
  },
  {
    name: '<PERSON><PERSON>',
    src: '/employer/avatar-darrell.webp',
    description: 'turned a trial task into a contract within 48 hours.',
  },
  {
    name: '<PERSON>',
    src: '/employer/avatar-jerome.webp',
    description:
      'used her fit report to target the right team and skipped screening.',
  },
  {
    name: '<PERSON>',
    src: '/employer/avatar-marvin.jpg',
    description:
      'matched a hiring bar on the first try and fast-tracked to onsite.',
  },
  {
    name: '<PERSON>',
    src: '/employer/avatar-theresa.jpeg',
    description:
      'showed real portfolio proof and received two invites in one week.',
  },
  {
    name: 'Sam',
    src: '/employer/avatar-jerome.webp',
    description:
      'got referred internally after sharing his top strengths summary.',
  },
  {
    name: 'Noah',
    src: '/employer/avatar-darrell.webp',
    description:
      'passed initial rounds by attaching the simulation score to his CV.',
  },
  {
    name: 'Linh',
    src: '/employer/avatar-jacob.jpeg',
    description:
      'negotiated a higher band after sharing quantified skill proof.',
  },
];

const companieLogos = [
  '/company/adapt-it.png',
  '/company/auckland.png',
  '/company/bnz.png',
  '/company/bourne.png',
  '/company/c65-soft.png',
  '/company/common-wealth-bank.png',
  '/company/contact.png',
  '/company/food-stuffs.png',
  '/company/foster-moore.png',
  '/company/idx.png',
  '/company/info-track.png',
  '/company/integration-work.png',
  '/company/j-l.png',
  '/company/kathmandu.png',
  '/company/kpmg.png',
  '/company/me.png',
  '/company/nab.png',
  '/company/nova-energie.png',
  '/company/nsw.png',
  '/company/ntt.png',
  '/company/orion.png',
  '/company/plan-it.png',
  '/company/republic.png',
  '/company/rvc.png',
  '/company/sedgwick.png',
  '/company/sky.png',
  '/company/sound-software.png',
  '/company/stat-lab.png',
  '/company/straker.png',
  '/company/sweet-spot.png',
  '/company/synlait.png',
  '/company/tower.png',
  '/company/adapt-it.png',
  '/company/auckland.png',
  '/company/bnz.png',
  '/company/bourne.png',
  '/company/c65-soft.png',
  '/company/common-wealth-bank.png',
  '/company/contact.png',
  '/company/food-stuffs.png',
  '/company/foster-moore.png',
  '/company/idx.png',
  '/company/info-track.png',
  '/company/integration-work.png',
  '/company/j-l.png',
];

function pickN<T>(arr: T[], n: number): T[] {
  const copy = arr.slice();
  for (let i = copy.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [copy[i], copy[j]] = [copy[j], copy[i]];
  }
  return copy.slice(0, n);
}

export default function RecentWins() {
  // tránh mismatch SSR: khởi tạo 3 dòng đầu, sau mount sẽ random
  const [wins, setWins] = useState(ALL_WINS.slice(0, 3));

  useEffect(() => {
    setWins(pickN(ALL_WINS, 3));
  }, []);

  // const cells = 10;
  // const items: Logo[] =
  //   logos.length > 0
  //     ? logos.slice(0, cells)
  //     : Array.from({ length: cells }).map(() => ({}));

  return (
    <section className="z-10 mx-auto w-full max-w-[1440px] px-4 py-8 pt-12 xl:px-0">
      <div className="grid grid-cols-1 items-start gap-8 lg:grid-cols-[1fr_420px] lg:gap-12">
        <div>
          <div className="font-bold">
            Hiring happened at companies like these.
          </div>
          <div className="mt-4 flex flex-wrap gap-4">
            {companieLogos.map((logo, idx) => (
              <span key={idx}>
                <Image
                  src={logo}
                  alt="company-logo"
                  width={60}
                  height={60}
                  className="h-12 w-12 rounded-full border border-[#E8E8E8] object-cover"
                  loader={({ src }) => src}
                />
              </span>
            ))}
          </div>
        </div>
        {/* <div className="w-full overflow-hidden">
          <div className="no-scrollbar w-full overflow-x-auto">
            <div className="flex w-max flex-nowrap gap-4 pb-4">
              {items.map((logo, idx) => (
                <div
                  key={idx}
                  className="flex h-14 w-28 flex-shrink-0 items-center justify-center rounded-xl border-2 border-dashed border-slate-300 bg-white sm:w-32"
                >
                  {logo.src ? (
                    <img
                      src={logo.src}
                      alt={logo.name ?? `logo-${idx + 1}`}
                      className="max-h-8 max-w-[80%] object-contain opacity-80"
                    />
                  ) : (
                    <div className="h-6 w-16 rounded-md bg-slate-100" />
                  )}
                </div>
              ))}
            </div>
          </div>

          <p className="mt-1 text-sm text-slate-500">
            Hiring happened at companies like these.
          </p>
        </div> */}

        <div className="">
          <div className="font-bold">Recent wins</div>

          <ul className="mt-3 space-y-3 text-sm text-slate-700">
            {wins.map((line, i) => (
              <li
                key={i}
                className="flex items-start gap-3 rounded-lg bg-white p-3"
              >
                <Avatar src={line.src} name={'avatar'} customSize={40} />
                <span className="flex-1">
                  <b>{line.name}</b> {line.description}
                </span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </section>
  );
}
