import React from 'react';

function EditSquareIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="18"
      height="20"
      viewBox="0 0 18 20"
      fill="none"
      {...props}
    >
      <path
        d="M16.3 10.9027V6.238C16.3 4.7134 16.3 3.952 16.0588 3.3427C15.6718 2.3644 14.851 1.5922 13.8115 1.2277C13.1653 1 12.3553 1 10.7362 1C7.903 1 6.4864 1 5.3542 1.3969C3.5362 2.0359 2.1007 3.3868 1.4212 5.0995C1 6.166 1 7.4989 1 10.1665V12.4579C1 15.2209 1 16.6024 1.7632 17.5627C1.9819 17.8372 2.2402 18.0811 2.5327 18.2872C3.3535 18.8659 4.465 18.9784 6.4 19M1 10C1 9.20443 1.31604 8.44144 1.87859 7.87889C2.44114 7.31634 3.20413 7.0003 3.9997 7.0003C4.5991 7.0003 5.3056 7.1047 5.8879 6.949C6.14234 6.88053 6.3743 6.74637 6.56054 6.55997C6.74678 6.37358 6.88074 6.1415 6.949 5.887C7.1047 5.3047 7.0003 4.5982 7.0003 3.9988C7.00054 3.20339 7.31668 2.44063 7.87921 1.87827C8.44174 1.31592 9.20459 1 10 1M12.6928 15.85H12.7018M16.714 14.9635C17.038 15.3379 17.2 15.5251 17.2 15.85C17.2 16.1749 17.038 16.363 16.714 16.7365C15.904 17.668 14.4433 19 12.7 19C10.9567 19 9.4951 17.668 8.686 16.7365C8.362 16.3621 8.2 16.1758 8.2 15.85C8.2 15.5251 8.362 15.337 8.686 14.9635C9.496 14.032 10.9567 12.7 12.7 12.7C14.4433 12.7 15.9049 14.032 16.714 14.9635Z"
        stroke="#161616"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export default EditSquareIcon;
