export const API_DOMAINS = {
  BASE_URL: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000',
  BASE_API: process.env.NEXT_PUBLIC_BASE_API || 'http://localhost:8080',

  AGENTOS_CLOUD_API:
    process.env.NEXT_PUBLIC_AGENTOS_CLOUD_API ||
    'https://data-api.agentos.cloud',

  AGENTOS_CLOUD:
    process.env.NEXT_PUBLIC_AGENTOS_CLOUD || 'https://dev-client.agentos.cloud',
};

export const API_ENDPONTS = {
  // Authentication
  SIGN_UP_REQUEST_CODE: '/api/v1/auth/signup/request-code',
  SIGN_UP_VERIFY: '/api/v1/auth/signup/verify',
  SIGN_IN_PASSWORD: '/api/v1/auth/login/password',
  SIGN_IN_REQUEST_CODE: '/api/v1/auth/login/request-code',
  SIGN_IN_VERIFY: '/api/v1/auth/login/verify',
  REFRESH_TOKEN: '/api/v1/auth/refresh',

  // User
  USER_PROFILE: '/api/v1/users/me',
  USER_TALENT: '/api/v1/users/talent',

  // User Profile
  GET_USER_PROFILE: '/api/v1/profiles/:userId',
  UPSERT_USER_PROFILE: '/api/v1/profiles/upsert',
  UPDATE_CV: '/api/v1/profiles/cv',

  // Job
  JOBS_LIST: '/api/v1/jobs',
  GET_BY_JOBID: '/api/v1/jobs/:jobId',
  JOB_PROCESS_SIMULATION: '/api/v1/jobs/:jobId/process-simulation',
  ORG_JOBS_LIST: '/api/v1/jobs/org',
  GET_ORG_PUBLIC_JOBS: '/api/v1/jobs/org/:orgId/home-jobs',
  GET_ORG_PUBLIC_JOB: '/api/v1/jobs/org/:orgId/:jobId',
  FAVORITE_JOB: '/api/v1/jobs/:jobId/favorite',
  UNFAVORITE_JOB: '/api/v1/jobs/:jobId/unfavorite',

  // Organization
  GET_ORG: '/api/v1/organizations',
  GET_ORG_BY_OWNER: '/api/v1/organizations/owner/:ownerId',
  GET_ORG_BY_ADMIN: '/api/v1/organizations/:orgId/admin',
  GET_ORG_BY_ID: '/api/v1/organizations/:orgId',
  UPDATE_COMPANY: '/api/v1/organizations/:id/company',
  UPDATE_MEDIA: '/api/v1/organizations/:id/media',

  // Candidate
  GET_CANDIDATES_BY_JOB: '/api/v1/job-candidates/job/:jobId',
  GET_CANDIDATES_BY_ORG: '/api/v1/job-candidates/org/:orgId',
  GET_CANDIDATE_BY_ORG:
    '/api/v1/job-candidates/org/:orgId/candidate/:candidateId',
  GET_CANDIDATES_FOR_ADMIN: '/api/v1/job-candidates/admin',

  CHECK_APPLIED: '/api/v1/job-candidates/apply/check',
  APPLY_SIMULATION: '/api/v1/job-candidates/apply/simulation',
  APPLY_CV: '/api/v1/job-candidates/apply/cv',
  PROCESS_CV_TO_APPLY: '/api/v1/job-candidates/apply/process-cv',

  GET_MY_APPLICATIONS: '/api/v1/job-candidates/my-applications',
  WITHDRAW_APPLICATION: '/api/v1/job-candidates/my-applications/:id/withdraw',

  // Simulation
  GET_ORG_HOME_SIMULATIONS: '/api/v1/simulations/org/:orgId/home-sims',
  GET_ORG_PUBLIC_SIMULATION: '/api/v1/simulations/org/:orgId/:simId',
  GET_ORG_ADMIN_SIMULATION: '/api/v1/simulations/org/:orgId/admin-sims',
  CREATE_ORG_SIMULATIONS: '/api/v1/simulations/org/:orgId',

  // Shortlist
  LIST_BY_ORG: '/api/v1/shortlists/org/:orgId',
  CREATE_SHORTLIST: '/api/v1/shortlists',
  UPDATE_SHORTLIST: '/api/v1/shortlists/:id',
  DELETE_SHORTLIST: '/api/v1/shortlists/:id/org/:orgId',

  // Shortlist Candidate
  CREATE_SHORTLIST_CANDIDATE: '/api/v1/shortlist-candidates',
  BULK_CREATE_SHORTLIST_CANDIDATE: '/api/v1/shortlist-candidates/bulk',
  BULK_DELETE_SHORTLIST_CANDIDATE: '/api/v1/shortlist-candidates/bulk-delete',
  DELETE_SHORTLIST_CANDIDATE_BY_CANDIDATE:
    '/api/v1/shortlist-candidates/candidate/:candidateId',
  GET_SHORTLIST_CANDIDATE_BY_ORG: '/api/v1/shortlist-candidates/org/:orgId',
  DELETE_SHORTLIST_CANDIDATE_BY_ORG:
    '/api/v1/shortlist-candidates/:id/org/:orgId',
};
