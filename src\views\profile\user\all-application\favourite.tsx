'use client';

import { Table, Badge, Checkbox } from 'rizzui';
import Image from 'next/image';
import Pagination from '@/views/pagination';
import { useState } from 'react';
import cn from '@/utils/class-names';
import MoneyIcon from '@/views/icons/money';
import ClockIcon from '@/views/icons/clock';
import TechnologyIcon from '@/views/icons/technology';
import {
  getJobTypeString,
  levelStyle,
  simulationLevel,
} from '@/views/job/job-list';
import HeartRegularIcon from '@/views/icons/heart-regular';
import { safeFormatDistanceToNow } from '@/utils/date';

const jobs = [
  {
    id: 1,
    companyName: 'Barone LLC.',
    location: '345 Park Avenue, USA',
    postedTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    salary: '9,500 - 11,500',
    jobType: 'Full time',
    simulation: {
      id: 1,
      level: 1,
      title: 'Software Engineer Simulation',
      point: 59,
      status: 'passed',
      minute: 15,
    },
    categories: ['Technology'],
    aiReview: [
      'Strong technical background with 7+ years in full-stack and team leadership.',
      'Job Simulation score 84% shows solid, scalable coding.',
      'CV lacks measurable results and DevOps mention.',
      'Overall, a high-potential candidate with minor improvement areas.',
    ],
    status: 'Draft',
    aiVerdict: 'Strong in real-world delivery. Web3-native.',
    matches: 86,
    duration: 'Beginner: 15 mins',
    companyLogoUrl: '/employer/avatar-theresa.jpeg',
    isHybrid: true,
  },
  {
    id: 2,
    companyName: 'Binford Ltd.',
    location: '345 Park Avenue, USA',
    postedTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    salary: '9,500 - 11,500',
    jobType: 'Full time',
    simulation: {
      id: 2,
      level: 2,
      title: 'Software Engineer Simulation',
      point: 93,
      status: 'passed',
      minute: 30,
    },
    categories: ['Technology'],
    aiReview: [
      'Strong technical background with 7+ years in full-stack and team leadership.',
      'Job Simulation score 84% shows solid, scalable coding.',
      'CV lacks measurable results and DevOps mention.',
      'Overall, a high-potential candidate with minor improvement areas.',
    ],
    status: 'Draft',
    aiVerdict: 'Strong in real-world delivery. Web3-native.',
    matches: 79,
    duration: 'Intermediate: 30 mins',
    companyLogoUrl: '/employer/avatar-jacob.jpeg',
    isFullTime: true,
  },
  {
    id: 3,
    companyName: 'Biffco Enterprises Ltd.',
    location: '345 Park Avenue, USA',
    postedTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    salary: '9,500 - 11,500',
    jobType: 'Full time',
    simulation: {
      id: 3,
      level: 3,
      title: 'Software Engineer Simulation',
      point: 45,
      status: 'passed',
      minute: 45,
    },
    categories: ['Technology'],
    aiReview: [
      'Strong technical background with 7+ years in full-stack and team leadership.',
      'Job Simulation score 84% shows solid, scalable coding.',
      'CV lacks measurable results and DevOps mention.',
      'Overall, a high-potential candidate with minor improvement areas.',
    ],
    status: 'Draft',
    aiVerdict: 'Strong in real-world delivery. Web3-native.',
    matches: 15,
    duration: 'Advanced: 45 mins',
    companyLogoUrl: '/employer/avatar-arlene.jpeg',
    isOnSite: true,
  },
  {
    id: 4,
    companyName: 'Adobe Inc',
    location: '345 Park Avenue, USA',
    postedTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    salary: '9,500 - 11,500',
    jobType: 'Full time',
    simulation: {
      id: 4,
      level: 1,
      title: 'Software Engineer Simulation',
      point: 93,
      status: 'passed',
      minute: 15,
    },
    categories: ['Technology'],
    aiReview: [
      'Strong technical background with 7+ years in full-stack and team leadership.',
      'Job Simulation score 84% shows solid, scalable coding.',
      'CV lacks measurable results and DevOps mention.',
      'Overall, a high-potential candidate with minor improvement areas.',
    ],
    status: 'Draft',
    aiVerdict: 'Strong in real-world delivery. Web3-native.',
    matches: 85,
    duration: 'Beginner: 15 mins',
    companyLogoUrl: '/employer/avatar-darrell.webp',
    hasContract: true,
  },
  {
    id: 5,
    companyName: 'Biffco Enterprises Ltd.',
    location: '345 Park Avenue, USA',
    postedTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    salary: '9,500 - 11,500',
    jobType: 'Full time',
    simulation: {
      id: 5,
      level: 2,
      title: 'Software Engineer Simulation',
      point: 93,
      status: 'failed',
      minute: 30,
    },
    categories: ['Technology'],
    aiReview: [
      'Strong technical background with 7+ years in full-stack and team leadership.',
      'Job Simulation score 84% shows solid, scalable coding.',
      'CV lacks measurable results and DevOps mention.',
      'Overall, a high-potential candidate with minor improvement areas.',
    ],
    status: 'Draft',
    aiVerdict: 'Strong in real-world delivery. Web3-native.',
    matches: 86,
    duration: 'Intermediate: 30 mins',
    companyLogoUrl: '/employer/avatar-theresa.jpeg',
    isOnSite: true,
  },
  {
    id: 6,
    companyName: 'Big Kahuna Burger Ltd.',
    location: '345 Park Avenue, USA',
    postedTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    salary: '9,500 - 11,500',
    jobType: 'Full time',
    simulation: {
      id: 6,
      level: 3,
      title: 'Software Engineer Simulation',
      point: 43,
      status: 'inProgress',
      minute: 45,
    },
    categories: ['Technology'],
    aiReview: [
      'Strong technical background with 7+ years in full-stack and team leadership.',
      'Job Simulation score 84% shows solid, scalable coding.',
      'CV lacks measurable results and DevOps mention.',
      'Overall, a high-potential candidate with minor improvement areas.',
    ],
    status: 'Draft',
    aiVerdict: 'Strong in real-world delivery. Web3-native.',
    matches: 86,
    duration: 'Advanced: 45 mins',
    companyLogoUrl: '/employer/avatar-ralph.jpg',
    isFullTime: true,
  },
];

const badgeClasses: Record<string, string> = {
  failed: 'bg-[#E8E8E8] text-[#161616]',
  inProgress: 'bg-[#FFEADA] text-[#EE6C0F]',
  passed: 'bg-[#98FFDC] text-[#008457]',
};

export default function Favourite() {
  const [page, setPage] = useState(1);

  return (
    <div>
      <Table variant="modern">
        <Table.Header className="rounded-t-xl !bg-[#FAFAFA]">
          <Table.Row className="rounded-t-xl">
            <Table.Head className="w-16"></Table.Head>
            <Table.Head>Job title</Table.Head>
            <Table.Head>Matches</Table.Head>
            <Table.Head>Simulation</Table.Head>
          </Table.Row>
        </Table.Header>

        <Table.Body>
          {jobs.map((job, index) => (
            <Table.Row key={index}>
              <Table.Cell>
                <Checkbox variant="flat" size="sm" />
              </Table.Cell>

              <Table.Cell>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div>
                      {job?.companyLogoUrl ? (
                        <Image
                          src={job.companyLogoUrl}
                          alt={job.companyName || 'Company Logo'}
                          width={60}
                          height={60}
                          className="h-15 w-15 rounded-full object-contain"
                          loader={({ src }) => src}
                        />
                      ) : (
                        <div
                          className="!h-15 !w-15 rounded-full bg-gray-100"
                          style={{ width: '60px', height: '60px' }}
                        />
                      )}
                    </div>

                    <div>
                      <div>
                        <p className="font-bold text-gray-700">
                          {job?.companyName}
                        </p>
                        <p className="text-[12px] text-gray-500">
                          {job?.location} •{' '}
                          {safeFormatDistanceToNow(new Date(job?.postedTime), {
                            addSuffix: true,
                          })}
                        </p>
                      </div>

                      <div className="flex flex-wrap items-center gap-2 text-[10px] text-gray-500">
                        <span className="flex items-center gap-1">
                          <MoneyIcon className="h-3 w-3" />
                          <span>{job?.salary || '-'}</span>
                        </span>
                        <span className="flex items-center gap-1">
                          <ClockIcon className="h-3 w-3" />
                          <span>{getJobTypeString(job as never) || '-'}</span>
                        </span>
                        <span className="flex items-center gap-1">
                          <TechnologyIcon className="h-3 w-3" />
                          {Array.isArray(job?.categories) &&
                          job?.categories?.length > 0 ? (
                            <span>{job.categories.join(', ')}</span>
                          ) : (
                            '-'
                          )}
                        </span>
                        {job?.simulation?.level && (
                          <span
                            className={`${levelStyle[simulationLevel[Number(job.simulation.level)]]} whitespace-nowrap rounded-full px-1`}
                          >
                            {simulationLevel[Number(job.simulation.level)]}:{' '}
                            <b>{job.simulation.minute} mins</b>
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  <div>
                    <div>AI review</div>
                    <ul className="list-disc space-y-1 pl-5 text-sm text-gray-600">
                      {job.aiReview.map((review, idx) => (
                        <li key={idx}>{review}</li>
                      ))}
                    </ul>
                    <div>AI Verdict: {job.aiVerdict}</div>
                  </div>

                  <div className="flex gap-4">
                    <button className="rounded border border-[#00B074] px-3 py-1 text-sm text-black hover:bg-primary hover:text-white">
                      View detail
                    </button>
                    <button className="flex items-center gap-2 rounded border border-[#00B074] px-3 py-1 text-sm text-black hover:bg-primary hover:text-white">
                      <HeartRegularIcon className="h-4 w-4" />
                      <span>Remove Favourites</span>
                    </button>
                  </div>
                </div>
              </Table.Cell>

              <Table.Cell>
                <span className="text-sm font-bold">{job.matches}%</span>
              </Table.Cell>

              <Table.Cell>
                <Badge
                  variant="flat"
                  size="sm"
                  className={cn(
                    badgeClasses[job.simulation.status],
                    'text-[12px] font-medium'
                  )}
                >
                  {job.simulation.status} ({job.simulation.point}%)
                </Badge>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>

      <hr className="border-t border-gray-100 pt-4" />

      <Pagination
        total={10}
        current={page}
        pageSize={3}
        defaultCurrent={1}
        showLessItems={true}
        onChange={(page: number) => setPage(page)}
        prevIconClassName="py-0 text-gray-500 !leading-[26px]"
        nextIconClassName="py-0 text-gray-500 !leading-[26px]"
        variant="solid"
        className="justify-center [&>.rc-pagination-item-active>a]:!text-white [&>.rc-pagination-item-active]:!border-primary [&>.rc-pagination-item-active]:!bg-primary [&>.rc-pagination-item-active]:!text-white [&>.rc-pagination-item:not(.rc-pagination-item-active)>a]:text-black [&>.rc-pagination-item:not(.rc-pagination-item-active)]:bg-transparent [&>.rc-pagination-item]:rounded-lg"
        // disabled={isLoading}
      />
    </div>
  );
}
